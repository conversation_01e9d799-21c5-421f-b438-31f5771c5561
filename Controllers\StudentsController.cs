using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.Models.Dtos;
using StudentManagementAPI.service;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using static StudentManagementAPI.Controllers.ApplicationController;
using static System.Runtime.InteropServices.JavaScript.JSType;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class StudentsController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IWebHostEnvironment _hostingEnvironment;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILanguageService _languageService;
    private readonly IConfiguration _configuration;
    private readonly ILogger<StudentsController> _logger;
    private readonly getuni2023 _uniService;

    public StudentsController(
            getuni2023 uniService,
        ApplicationDbContext context,
        IWebHostEnvironment hostingEnvironment,
        IHttpContextAccessor httpContextAccessor,
        UserManager<ApplicationUser> userManager,
        ILanguageService languageService,
        IConfiguration configuration,
        ILogger<StudentsController> logger)
    {
        _uniService = uniService;
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _hostingEnvironment = hostingEnvironment ?? throw new ArgumentNullException(nameof(hostingEnvironment));
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _languageService = languageService ?? throw new ArgumentNullException(nameof(languageService));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    // GET: api/Students
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Student>>> GetStudents([FromQuery] string lang = "en")
    {
        try
        {
            var language = _languageService.ValidateLanguage(lang);
            var currentUser = await _userManager.GetUserAsync(User);

            if (currentUser == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language),
                    Language = language
                });
            }

            var students = await _context.Students.ToListAsync();
            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Data = students,
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetStudents");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = "Internal server error",
                Language = lang
            });
        }
    }

    private async Task<string> GetCurrentStudentId()
    {
        if (!User.Identity.IsAuthenticated)
            return null;

        var username = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (string.IsNullOrEmpty(username))
            return null;

        var user = await _userManager.FindByNameAsync(username); // 

        if (user == null)
            return null;

        var student = await _context.Students
            .FirstOrDefaultAsync(s => s.Id == user.Id);

        return student?.Id;
    }
    // GET: api/Students/5
    [HttpGet("byid")]
    public async Task<ActionResult<Student>> GetStudent( [FromQuery] string lang = "en")
    {
        try
        {
            var language = _languageService.ValidateLanguage(lang);
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = "User is not associated with a student account"
                });
            }


            var student = await _context.Students
                .Where(s => s.Id == studentId)
                .Select(s => new
                {s.Point,
                    s.RefCode,
                    s.Id,
                    s.FirstName,
                    s.LastName,
                    s.Email,
                    s.PhoneNumber,
                    s.DateOfBirth,
                    s.CountryOfResidence,
                    s.Citizenship,
                    s.PassportNumber,
                    s.FatherName,
                    s.Mothername,
                    s.RegistrationType,
                    s.SecondarySchoolCountry,
                    s.SecondarySchoolName,
                    s.TCKimlikNumber,
                    s.Gender,
                    s.CurrentStage,
                    s.SchoolOrUniversityName,
                    s.DestinationCountry,
                    s.DegreeInterest,
                    s.Language,
                    s.FieldOfStudyInterest,
                    s.InterestedUniversities,
                    s.EnrollmentDate,
                    s.AddressCountry,
                    s.AddressCity,
                    s.AddressStreet,
                    s.CrmId,
                    s.ImageURL,
                    Consultant = s.Consultant != null ? new 
                    {
                        s.Consultant.Id,
                        s.Consultant.FirstName,
                        s.Consultant.LastName,
                        s.Consultant.PhoneNumber,
                        s.Consultant.ImageURL,
                        s.Consultant.Description,
                        s.Consultant.DateCreated,
                        s.Consultant.DateModified
                    } : null
                })
                .FirstOrDefaultAsync();


            if (student == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = _languageService.GetMessage("StudentNotFound", language),
                    Language = language
                });
            }

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Data = student,
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetStudent");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = "Internal server error",
                Language = lang
            });
        }
    }
    [AllowAnonymous]
    [HttpPut("UpdateStudentcosultant")]
    public async Task<IActionResult> UpdateStudentcosultant([FromQuery] string accountid ,string owenerid)
    {
        var language = "en";
        try
        {
           
            var studentId = _context.Students.Where(a=>a.CrmId==accountid).FirstOrDefault();

            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = "User is not associated with a student account",
                    Language = language
                });
            }
            int owerner=  _context.Consultants.Where(a=>a.SalesforceId==owenerid).Select(a=>a.Id).FirstOrDefault();
            var existingStudent = await _context.Students.FindAsync(studentId.Id);
            if (existingStudent == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = _languageService.GetMessage("StudentNotFound", language),
                    Language = language
                });
            }

            // Update only the fields that are provided in the DTO
        
            if (owenerid!=null) existingStudent.ConsultantId = owerner;

            // Update audit fields
            existingStudent.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();
            existingStudent.datemodified = DateTime.Now;

            _context.Entry(existingStudent).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
              
                    throw;
                
            }

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = _languageService.GetMessage("StudentUpdated", language) ?? "Student updated successfully",
                Data = existingStudent,
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateStudent");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = "Internal server error: " + ex.Message,
                Language = language
            });
        }
    }
    // PUT: api/Students
    [HttpPut]
    public async Task<IActionResult> UpdateStudent([FromBody] StudentUpdateDto studentUpdate, [FromQuery] string lang = "en")
    {
        try
        {
            var language = _languageService.ValidateLanguage(lang);
            var studentId = await GetCurrentStudentId();
            
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = "User is not associated with a student account",
                    Language = language
                });
            }

            var existingStudent = await _context.Students.FindAsync(studentId);
            if (existingStudent == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = _languageService.GetMessage("StudentNotFound", language),
                    Language = language
                });
            }

            // Update only the fields that are provided in the DTO
            if (studentUpdate.FirstName != null) existingStudent.FirstName = studentUpdate.FirstName;
            if (studentUpdate.LastName != null) existingStudent.LastName = studentUpdate.LastName;
            if (studentUpdate.PhoneNumber != null) existingStudent.PhoneNumber = studentUpdate.PhoneNumber;
            if (studentUpdate.Email != null) existingStudent.Email = studentUpdate.Email;
            if (studentUpdate.DateOfBirth.HasValue) existingStudent.DateOfBirth = studentUpdate.DateOfBirth.Value;
            if (studentUpdate.CountryOfResidence != null) existingStudent.CountryOfResidence = studentUpdate.CountryOfResidence;
            if (studentUpdate.Citizenship != null) existingStudent.Citizenship = studentUpdate.Citizenship;
            if (studentUpdate.PassportNumber != null) existingStudent.PassportNumber = studentUpdate.PassportNumber;
            if (studentUpdate.FatherName != null) existingStudent.FatherName = studentUpdate.FatherName;
            if (studentUpdate.Mothername != null) existingStudent.Mothername = studentUpdate.Mothername;
            if (studentUpdate.SecondarySchoolCountry != null) existingStudent.SecondarySchoolCountry = studentUpdate.SecondarySchoolCountry;
            if (studentUpdate.SecondarySchoolName != null) existingStudent.SecondarySchoolName = studentUpdate.SecondarySchoolName;
            if (studentUpdate.TCKimlikNumber != null) existingStudent.TCKimlikNumber = studentUpdate.TCKimlikNumber;
            if (studentUpdate.Gender != null) existingStudent.Gender = studentUpdate.Gender;
            if (studentUpdate.SchoolOrUniversityName != null) existingStudent.SchoolOrUniversityName = studentUpdate.SchoolOrUniversityName;
            if (studentUpdate.DestinationCountry != null) existingStudent.DestinationCountry = studentUpdate.DestinationCountry;
            if (studentUpdate.DegreeInterest != null) existingStudent.DegreeInterest = studentUpdate.DegreeInterest;
            if (studentUpdate.Language != null) existingStudent.Language = studentUpdate.Language;
            if (studentUpdate.FieldOfStudyInterest != null) existingStudent.FieldOfStudyInterest = studentUpdate.FieldOfStudyInterest;
            if (studentUpdate.InterestedUniversities != null) existingStudent.InterestedUniversities = studentUpdate.InterestedUniversities;
            if (studentUpdate.CurrentStage != null) existingStudent.CurrentStage = studentUpdate.CurrentStage;
            if (studentUpdate.RegistrationType != null) existingStudent.RegistrationType = studentUpdate.RegistrationType;
            if (studentUpdate.ConsultantId.HasValue) existingStudent.ConsultantId = studentUpdate.ConsultantId.Value;
            
            // Update audit fields
            existingStudent.modifiedIP = HttpContext.Connection.RemoteIpAddress?.ToString();
            existingStudent.datemodified = DateTime.Now;

            _context.Entry(existingStudent).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
             

            catch (DbUpdateConcurrencyException)
            {
                if (!StudentExists(studentId))
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("StudentNotFound", language),
                        Language = language
                    });
                }
                else
                {
                    throw;
                }
            }
            Updateaccountstudent(studentUpdate.DegreeInterest, studentUpdate.CountryOfResidence, studentUpdate.RegistrationType, studentUpdate.Mothername, studentUpdate.PassportNumber, studentUpdate.FatherName, _uniService.Countryen(studentUpdate.SecondarySchoolName), studentUpdate.Gender, studentUpdate.DateOfBirth?.ToString("yyyy-MM-dd"), existingStudent.CrmId, existingStudent.AddressCity, _uniService.Countryen(existingStudent.AddressCountry), existingStudent.AddressStreet);

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = _languageService.GetMessage("StudentUpdated", language) ?? "Student updated successfully",
                Data = existingStudent,
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateStudent");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = "Internal server error: " + ex.Message,
                Language = lang
            });
        }
    }
    async Task<string> Updateaccountstudent(string Preferred_Degree__pc, string Country_Phone_Code__pc1, string Registration_Type__pc, string Mother_Name__pc, string Passport__pc, string Father_Name__pc, string Country_of_Secondary_School__pc, string Gender__pc, string PersonBirthdate, string accountid, string Address_City__pc
, string Address_Country__pc, string Address_Street__pc)
    {

        string sObject = "Account";
        object body = new object();


        body = new
        {
            PersonBirthdate = PersonBirthdate,
            Country_of_Secondary_School__pc = Country_of_Secondary_School__pc,

            Gender__pc = Gender__pc,
            Father_Name__pc = Father_Name__pc,
            Mother_Name__pc = Mother_Name__pc,
            Passport__pc = Passport__pc,
            RecordTypeId = "0128d000000Z2vvAAC",

            Address_City__pc = Address_City__pc,

            Address_Country__pc = Address_Country__pc,
            Address_Street__pc = Address_Street__pc,


            Country_Phone_Code__pc = Country_Phone_Code__pc1,



            Preferred_Degree__pc = Preferred_Degree__pc,

            Branch__c = "United Main",

            Registration_Type__pc = Registration_Type__pc,

            OwnerId = "0054L000001IJ70QAG",

        };



        //update
        return await _uniService.Update(sObject, accountid, body);
    }
    // PUT: api/Students/5
    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateStudentbyid(string id, [FromForm] Student student, [FromQuery] string lang = "en")
    {
        try
        {
            var language = _languageService.ValidateLanguage(lang);
            var studentId = await GetCurrentStudentId();
            
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language) ?? "User is not associated with a student account",
                    Language = language
                });
            }

            if (id != studentId)
            {
                return BadRequest(new ApiResponse
                {
                    Status = false,
                    Code = 400,
                    Message = _languageService.GetMessage("InvalidRequest", language) ?? "Invalid student ID",
                    Language = language
                });
            }

            // Handle file upload if present
            var file = Request.Form.Files.FirstOrDefault();
            if (file != null && file.Length > 0)
            {
                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();
                
                if (string.IsNullOrEmpty(fileExtension) || !allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = _languageService.GetMessage("InvalidFileType", language) ?? "Invalid file type. Only JPG, JPEG, PNG, and GIF are allowed.",
                        Language = language
                    });
                }

                // Create uploads directory if it doesn't exist
                var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "students");
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }

                // Generate unique filename using student ID
                var uniqueFileName = $"{studentId}{fileExtension}";
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                // Delete existing file if it exists
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Delete(filePath);
                }

                // Save the file
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // Update the student's image URL
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                student.ImageURL = $"{baseUrl}/uploads/students/{uniqueFileName}";
            }

            _context.Entry(student).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StudentExists(studentId))
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("StudentNotFound", language),
                        Language = language
                    });
                }
                throw;
            }

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = _languageService.GetMessage("Success", language),
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateStudentbyid");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = "Internal server error",
                Language = lang
            });
        }
    }

    // DELETE: api/Students/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteStudent(int id, [FromQuery] string lang = "en")
    {
        try
        {
            var language = _languageService.ValidateLanguage(lang);
            var currentUser = await _userManager.GetUserAsync(User);

            if (currentUser == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language),
                    Language = language
                });
            }

            var student = await _context.Students.FindAsync(id);
            if (student == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = _languageService.GetMessage("StudentNotFound", language),
                    Language = language
                });
            }

            _context.Students.Remove(student);
            await _context.SaveChangesAsync();

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = _languageService.GetMessage("Success", language),
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in DeleteStudent");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = "Internal server error",
                Language = lang
            });
        }
    }

    private bool StudentExists(string id)
    {
        return _context.Students.Any(e => e.Id == id);
    }

    // POST: api/Students/upload-image
    [HttpPost("upload-image")]
    public async Task<IActionResult> UpdateStudentImage(IFormFile file, [FromQuery] string lang = "en")
    {
        var language = _languageService.ValidateLanguage(lang);

        try
        {
            _logger.LogInformation("Starting image upload process");
            var studentId = await GetCurrentStudentId();

            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language) ?? "Unauthorized",
                    Language = language
                });
            }

            var student = await _context.Students.FindAsync(studentId);
            if (student == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = _languageService.GetMessage("StudentNotFound", language) ?? "Student not found",
                    Language = language
                });
            }

            if (file == null || file.Length == 0)
            {
                return BadRequest(new ApiResponse
                {
                    Status = false,
                    Code = 400,
                    Message = _languageService.GetMessage("NoFileUploaded", language) ?? "No file was uploaded",
                    Language = language
                });
            }

            // Validate file type
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var fileExtension = Path.GetExtension(file.FileName)?.ToLowerInvariant();

            _logger.LogInformation($"Processing file: {file.FileName}, Size: {file.Length} bytes, Type: {file.ContentType}");

            if (string.IsNullOrEmpty(fileExtension) || !allowedExtensions.Contains(fileExtension))
            {
                var errorMsg = $"Invalid file type: {fileExtension}. Allowed types: {string.Join(", ", allowedExtensions)}";
                _logger.LogWarning(errorMsg);

                return BadRequest(new ApiResponse
                {
                    Status = false,
                    Code = 400,
                    Message = _languageService.GetMessage("InvalidFileType", language) ?? "Invalid file type. Only JPG, JPEG, PNG, and GIF are allowed.",
                    Language = language
                });
            }

            // Create uploads directory if it doesn't exist
            var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "students");
            _logger.LogInformation($"Upload directory: {uploadsFolder}");

            try
            {
                if (!Directory.Exists(uploadsFolder))
                {
                    _logger.LogInformation("Creating uploads directory");
                    Directory.CreateDirectory(uploadsFolder);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create uploads directory");
                throw;
            }

            // Generate unique filename using student ID
            var uniqueFileName = $"{student.Id}{fileExtension}";
            var filePath = Path.Combine(uploadsFolder, uniqueFileName);

            // Delete existing file if it exists
            if (System.IO.File.Exists(filePath))
            {
                System.IO.File.Delete(filePath);
            }

            // Save the file
            try
            {
                _logger.LogInformation($"Saving file to: {filePath}");
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }
                _logger.LogInformation("File saved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving file");
                throw;
            }

            // Update the student's image URL
            var baseUrl = $"{Request.Scheme}://{Request.Host}";
            var imageUrl = $"{baseUrl}/uploads/students/{uniqueFileName}";
            student.ImageURL = imageUrl;

            _context.Entry(student).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StudentExists(studentId))
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("StudentNotFound", language) ?? "Student not found",
                        Language = language
                    });
                }
                throw;
            }

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = _languageService.GetMessage("ImageUpdated", language) ?? "Profile image updated successfully",
                Data = new { ImageURL = student.ImageURL },
                Language = language
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in UpdateStudentImage");
            return StatusCode(500, new ApiResponse
            {
                Status = false,
                Code = 500,
                Message = _languageService.GetMessage("InternalServerError", language) ?? "Internal server error: " + ex.Message,
                Language = language
            });
        }
    }
    public async Task<string> Updateaccountstudentmajors(string Selected_Result_Programs__c, string accountid)
    {

        string sObject = "Holland_Test__c";
        object body = new object();


        body = new
        {




            Selected_Result_Programs__c = Selected_Result_Programs__c






        };



        //update
        return await _uniService.Update(sObject, accountid, body);
    }
    public async Task<string> Updateaccountstudentdestination(string Account_Billing_Country__c
, string accountid)
    {

        string sObject = "Account";
        object body = new object();


        body = new
        {




            Destination_Country__pc
 = Account_Billing_Country__c







        };



        //update
        return await _uniService.Update(sObject, accountid, body);
    }
    [HttpPost("updatemajors")]
    public async Task<ActionResult<ApiResponse>> updatemajors([FromBody] RequestDatamajors data)
    {
        var studentId = await GetCurrentStudentId();
        if (studentId == null)
        {
            return Unauthorized(new ApiResponse
            {
                Status = false,
                Code = 401,
                Message = _languageService.GetMessage("Unauthorized", "en"),
                Language = "en"
            });
        }
        Student student = await _context.Students
         .FirstOrDefaultAsync(s => s.Id == studentId);

        string salaseid = await _context.TestAttempts
            .Where(t => t.StudentId == studentId && t.Id == data.TestAttemptId)
            .Select(t => t.SalseId)
            .FirstOrDefaultAsync();


        string appid = "";
        string color = "0";
        string salceid11 = "";
        string PersonContactId1 = "";



        salceid11 = student.CrmId;


        PersonContactId1 = PersonContactId1;
        await Updateaccountstudentmajors(data.Preferredmajors, salaseid);
        await Updateaccountstudentdestination(data.destination, salceid11);
        //if (student != null)
        //{
        //    student.DegreeInterest = data.PreferredDegree;

        //    student.RegistrationType = data.RegistrationType;
        //    _context.Students.Update(student);
        //    await _context.SaveChangesAsync();

        //}
        return Ok(new
        {
            msg = "ok",

            contactid = PersonContactId1,
            accountid = salceid11,
            appid = appid
        });






    }
    public class changePasswordRequest
    {
     

        [Required(ErrorMessage = "New password is required")]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        public string NewPassword { get; set; }
    }
    [HttpPost("Change-password")]
    public async Task<IActionResult> ResetPassword([FromBody] changePasswordRequest model)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        var studentId = await GetCurrentStudentId();
        if (studentId == null)
        {
            return Unauthorized(new ApiResponse
            {
                Status = false,
                Code = 401,
                Message = _languageService.GetMessage("Unauthorized", "en"),
                Language = "en"
            });
        }
        Student student = await _context.Students
         .FirstOrDefaultAsync(s => s.Id == studentId);

        var pass = _uniService.hashpassword(model.NewPassword);
        var user = await _userManager.FindByIdAsync(studentId);

        if (user == null)
        {
            return NotFound("User not found");
        }
        await _userManager.RemovePasswordAsync(user);
        var result = await _userManager.AddPasswordAsync(user, model.NewPassword);



        _uniService.updatepassword(student.CrmId, pass);

        if (!result.Succeeded)
        {
            // Return the first error
            return BadRequest(new { message = result.Errors.FirstOrDefault()?.Description ?? "Failed to reset password." });
        }

        return Ok(new { message = "Password has been reset successfully." });
    }

}

public class UpdateImageRequest
{
    [Required(ErrorMessage = "Image URL is required")]
    public string ImageURL { get; set; }
}