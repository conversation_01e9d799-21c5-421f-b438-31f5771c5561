﻿
    using Microsoft.AspNetCore.Mvc;
    using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using StudentManagementAPI.Services;
using System.ComponentModel.DataAnnotations;
using System.Net.Http;
using System.Text;
using System.Text.Json.Serialization;
using static StudentManagementAPI.Controllers.AuthController;
using static StudentManagementAPI.Controllers.SalesforceUsersController;


    namespace StudentManagementAPI.Controllers
    {


    


    [ApiController]
        [Route("api/[controller]")]
        public class SalesforceUsersController : ControllerBase
    {

        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<AuthController> _logger;
        private readonly IOptions<SalesforceSettings> _salesforceSettings;
        private readonly RsaHelper _uniService1;
        private readonly Data.ApplicationDbContext _context; // Add ApplicationDbContext

        public SalesforceUsersController(
               getuni2023 uniService,
                    Data.ApplicationDbContext context,
            RsaHelper uniService1,
        IHttpClientFactory httpClientFactory,
            IOptions<SalesforceSettings> salesforceSettings
       )
        {
            _context = context; // Assign injected context
            _uniService = uniService;
            _uniService1 = uniService1;
            _httpClientFactory = httpClientFactory;
            _salesforceSettings = salesforceSettings ?? throw new ArgumentNullException(nameof(salesforceSettings));

        }
        private readonly getuni2023 _uniService;
        public class SalesforceUser
        {
            [Key]
            public string Id { get; set; }

            [JsonPropertyName("FirstName")]
            public string? FirstName { get; set; }

            [JsonPropertyName("LastName")]
            public string? LastName { get; set; }

            [JsonPropertyName("Email")]
            public string Email { get; set; }

            [JsonPropertyName("MobilePhone")]
            public string? MobilePhone { get; set; }

            [JsonPropertyName("FullPhotoUrl")]
            public string? FullPhotoUrl { get; set; }

            [JsonPropertyName("AboutMe")]
            public string? AboutMe { get; set; }

            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        }


        // Models/SalesforceApiResponse.cs



        public class SalesforceApiResponse<T>
        {
            [JsonPropertyName("totalSize")]
            public int TotalSize { get; set; }

            [JsonPropertyName("done")]
            public bool Done { get; set; }

            [JsonPropertyName("records")]
            public List<T> Records { get; set; }
        }



        // Alternative 1: If you want to return a list of users
        public async Task<List<SalesforceUser>> SalesforceUserlist()
        {
            try
            {
                var accessToken = _uniService1.GetAccessToken();

                var client = _httpClientFactory.CreateClient();

                // Use the Salesforce SOQL query endpoint
                var query = "SELECT Id,FirstName,LastName,Email,MobilePhone,FullPhotoUrl,AboutMe FROM User";
                var encodedQuery = Uri.EscapeDataString(query);
                var url = $"https://vaha.my.salesforce.com/services/data/v57.0/query?q={encodedQuery}";

                var request = new HttpRequestMessage(HttpMethod.Get, url);

                // Set the Authorization header
                request.Headers.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                // Send the request
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                var responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Salesforce API Response: {responseBody}");

                // Deserialize Salesforce query response
                var result = Newtonsoft.Json.JsonConvert
                    .DeserializeObject<SalesforceApiResponse<SalesforceUser>>(responseBody);

                return result?.Records ?? new List<SalesforceUser>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SalesforceUserlist: {ex}");
                return new List<SalesforceUser>();
            }
        }



        // GET: api/salesforceusers/fetch-from-salesforce
        [HttpGet("insert-users")]
        public async Task<ActionResult<IEnumerable<SalesforceUser>>> FetchFromSalesforce()
        {
            try
            {
                // Add more detailed logging/debugging
                var users = await SalesforceUserlist();

                // More robust null checking
                if (users == null)
                {
                    return Ok(new { message = "SalesforceUserlist returned null", data = new List<SalesforceUser>() });
                }

                // Convert to list to avoid multiple enumeration and filter out any null items
                var userList = users.Where(u => u != null).ToList();

                if (!userList.Any())
                {
                    return Ok(new { message = "No valid users found in Salesforce", data = new List<SalesforceUser>() });
                }

                var insertedCount = 0;
                var updatedCount = 0;

                foreach (var sfUser in userList)
                {
                    try
                    {
                        // Additional null check for individual user
                        if (sfUser?.Id == null)
                        {
                            continue; // Skip this user if Id is null
                        }

                        // Check if consultant already exists (by Salesforce Id)
                        var existingConsultant = _context.Consultants
                            .FirstOrDefault(c => c.Email.Contains(sfUser.Email));

                        if (existingConsultant == null)
                        {
                            // Map SalesforceUser to Consultant with null-safe assignments
                            var consultant = new Consultant
                            {
                                SalesforceId = sfUser.Id,
                                FirstName = !string.IsNullOrWhiteSpace(sfUser.FirstName) ? sfUser.FirstName : "Unknown",
                                LastName = !string.IsNullOrWhiteSpace(sfUser.LastName) ? sfUser.LastName : "Unknown",
                                PhoneNumber = sfUser.MobilePhone,
                                ImageURL = sfUser.FullPhotoUrl,
                                Description = sfUser.AboutMe,
                                Email = sfUser.Email,
                                DateCreated = DateTime.Now,
                                DateModified = DateTime.Now
                            };

                            _context.Consultants.Add(consultant);
                            await _context.SaveChangesAsync();
                        }
                        else
                        {
                            try
                            {


                                var existingConsultant1 = _context.Consultants
                                .FirstOrDefault(c => c.Id == existingConsultant.Id);

                                // Track if any changes were made

                                existingConsultant1.FirstName = sfUser.FirstName;

                                existingConsultant1.LastName = sfUser.LastName;

                                existingConsultant1.PhoneNumber = sfUser.MobilePhone;

                                existingConsultant1.ImageURL = sfUser.FullPhotoUrl;

                                existingConsultant1.Description = sfUser.AboutMe;

                                existingConsultant1.DateModified = DateTime.Now;

                                existingConsultant1.Email = sfUser.Email;
                                _context.SaveChanges();
                            }
                            catch (Exception ex)
                            {

                                throw;
                            }

                        }
                    }
                    catch (Exception userEx)
                    {
                        // Log individual user processing errors but continue with others
                        // You might want to use a proper logger here
                        Console.WriteLine($"Error processing user {sfUser?.Id}: {userEx.Message}");
                        continue;
                    }
                }
                // Save all changes to database


                // Return the fetched users along with sync results
                return Ok(new
                {
                    message = $"Successfully synced {insertedCount} new and {updatedCount} updated consultants",
                    totalFetched = userList.Count,
                    newRecords = insertedCount,
                    updatedRecords = updatedCount,
                    data = userList
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = $"Error fetching users: {ex.Message}", stackTrace = ex.StackTrace });
            }
        }
        [HttpGet("update-users")]
        public async Task<ActionResult<IEnumerable<SalesforceUser>>> FetchFromSalesforceu()
        {
            try
            {
                // Add more detailed logging/debugging
                var users = await SalesforceUserlist();

                // More robust null checking
                if (users == null)
                {
                    return Ok(new { message = "SalesforceUserlist returned null", data = new List<SalesforceUser>() });
                }

                // Convert to list to avoid multiple enumeration and filter out any null items
                var userList = users.Where(u => u != null).ToList();

                if (!userList.Any())
                {
                    return Ok(new { message = "No valid users found in Salesforce", data = new List<SalesforceUser>() });
                }

                var insertedCount = 0;
                var updatedCount = 0;

                foreach (var sfUser in userList)
                {
                    try
                    {





                        var existingConsultant1 = _context.Consultants
                                .FirstOrDefault(c => c.SalesforceId == sfUser.Id);

                                // Track if any changes were made

                                existingConsultant1.FirstName = sfUser.FirstName;

                                existingConsultant1.LastName = sfUser.LastName;

                                existingConsultant1.PhoneNumber = sfUser.MobilePhone;

                                existingConsultant1.ImageURL = sfUser.FullPhotoUrl;

                                existingConsultant1.Description = sfUser.AboutMe;

                                existingConsultant1.DateModified = DateTime.Now;

                                existingConsultant1.Email = sfUser.Email;
                                _context.SaveChanges();


                    }
                    catch (Exception userEx)
                    {
                        // Log individual user processing errors but continue with others
                        // You might want to use a proper logger here
                        Console.WriteLine($"Error processing user {sfUser?.Id}: {userEx.Message}");
                        continue;
                    }

                }
                // Save all changes to database


                // Return the fetched users along with sync results
                return Ok(new
                {
                    message = $"Successfully synced {insertedCount} new and {updatedCount} updated consultants",
                    totalFetched = userList.Count,
                    newRecords = insertedCount,
                    updatedRecords = updatedCount,
                    data = userList
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = $"Error fetching users: {ex.Message}", stackTrace = ex.StackTrace });
            }
        }
    }
}

