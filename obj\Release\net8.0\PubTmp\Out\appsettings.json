{"Configuration": {"ApiKey": "JkGwZj3mQyT7vB8fN2xQz9LrP4dK2mV8Q6pZtH1bR5g="}, "Salesforce": {"Domain": "vaha.my.salesforce.com", "ApiVersion": "v57.0"}, "ConnectionStrings": {"DefaultConnection": "Data Source=92.205.235.245;Initial Catalog=StudentManagementAPIdb;User Id=waseem100_sit;Password=********;Pooling=False;TrustServerCertificate=True;", "TestContext": "Data Source=92.205.235.245;Initial Catalog=agency2024;User Id=waseem100_sit;Password=********;Pooling=False;TrustServerCertificate=True;", "UE": "Data Source=92.205.235.245;Initial Catalog=UE;User Id=Hwunitedsales;Password=***********;Pooling=False;TrustServerCertificate=True;", "UEalter": "Data Source=92.205.235.245;Initial Catalog=UE_AgentData;User Id=Hwunitedsales;Password=***********;Pooling=False;TrustServerCertificate=True;", "sit": "Data Source=92.205.235.245;Initial Catalog=u1843992_db8C9;User Id=u1843992_user8C9;Password=****************;Pooling=False;TrustServerCertificate=True;", "UniversityProgramsDb": "Data Source=92.205.235.245;Initial Catalog=static_data;User Id=waseem100_sit;Password=********;Pooling=False;TrustServerCertificate=True;"}, "Jwt": {"Key": "ThisIsMySecureKeyForJwtAuthentication123456789", "Issuer": "StudentManagementAPI", "Audience": "StudentManagementClient", "DurationInDays": 7}, "AppSettings": {"BaseUrl": "https://apiv2.unitededucation.com.tr"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Authentication": {"Google": {"ClientId": "564949071795-qpojb84fkfbuf2oo6f70i6hs7tdabev7.apps.googleusercontent.com", "ClientSecret": "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", "CallbackPath": "/external-login-callback"}, "Facebook": {"AppId": "1280911893631925", "AppSecret": "db35ddeffbc4b32a3853ff5d75af5cc9", "CallbackPath": "/external-login-callback"}, "Microsoft": {"ClientId": "YOUR_MICROSOFT_CLIENT_ID", "ClientSecret": "YOUR_MICROSOFT_CLIENT_SECRET", "TenantId": "common", "CallbackPath": "/external-login-callback"}, "Apple": {"ClientId": "YOUR_APPLE_SERVICE_ID", "KeyId": "YOUR_APPLE_KEY_IDENTIFIER", "TeamId": "YOUR_APPLE_TEAM_ID", "PrivateKey": "YOUR_APPLE_PRIVATE_KEY"}}, "AllowedHosts": "*"}