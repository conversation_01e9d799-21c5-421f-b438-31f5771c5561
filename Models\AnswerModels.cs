using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Models
{
    public class AnswerUpdateRequest
    {
        [Required]
        public string Value { get; set; }
    }

    public class AnswerSubmission
    {
        [Required]
        public int QuestionId { get; set; }
        
        [Required]
        public string Value { get; set; }
    }

    public class BatchAnswerRequest
    {
        [Required]
        public List<AnswerSubmission> Answers { get; set; }
    }
}