﻿using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Models
{
    public class TermSetting
    {
        [Key]
        public string Id { get; set; }
        public string Term_Name_Formula { get; set; }
        public string? Name { get; set; }              // nullable
        public string? Semester { get; set; }          // nullable
        public string? Academic_Year { get; set; }     // nullable
        public string? Term_Logo { get; set; }         // nullable
        public string? Degree { get; set; }            // nullable
        public bool Active { get; set; }              // nullable
        public string? Term_Settings { get; set; }     // nullable
    }

}
