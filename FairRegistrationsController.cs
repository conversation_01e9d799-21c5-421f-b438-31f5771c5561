﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System;
using System.Data.SqlTypes;
using System.Text.Json;
using System.Threading.Tasks;

namespace StudentManagementAPI
{
    public class FairRegistration
    {
        public int Id { get; set; }
        public string student_name { get; set; }
        public string student_phone { get; set; }
        public string student_email { get; set; }
        public string residence_city { get; set; }
        public string desired_major { get; set; }
        public string parent_name { get; set; }
        public string parent_phone { get; set; }
        public string education_level { get; set; }
        public string source { get; set; }
        public string fair_type { get; set; }
        public DateTime created_at { get; set; }
        public DateTime updated_at { get; set; }
    }

    [ApiController]
    [Route("api/[controller]")]
    public class FairRegistrationsController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public FairRegistrationsController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        [HttpPost]
        public async Task<IActionResult> InsertFairRegistration([FromBody] FairRegistration item)
        {
            if (item == null)
                return BadRequest("Invalid data.");

            string connectionString = _configuration.GetConnectionString("UEalter");

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var checkCmd = new SqlCommand("SELECT COUNT(*) FROM FairRegistrations WHERE Id = @Id", connection);
                    checkCmd.Parameters.AddWithValue("@Id", item.Id);
                    int exists = (int)await checkCmd.ExecuteScalarAsync();

                    if (exists > 0)
                        return Ok("Record already exists");

                    var insertCmd = new SqlCommand(@"
                        INSERT INTO FairRegistrations (
                            Id, student_name, student_phone, student_email, residence_city, desired_major,
                            parent_name, parent_phone, education_level, source, fair_type, created_at, updated_at
                        ) VALUES (
                            @Id, @student_name, @student_phone, @student_email, @residence_city, @desired_major,
                            @parent_name, @parent_phone, @education_level, @source, @fair_type, @created_at, @updated_at
                        )", connection);

                    insertCmd.Parameters.AddWithValue("@Id", item.Id);
                    insertCmd.Parameters.AddWithValue("@student_name", item.student_name ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@student_phone", item.student_phone ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@student_email", item.student_email ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@residence_city", item.residence_city ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@desired_major", item.desired_major ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@parent_name", item.parent_name ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@parent_phone", item.parent_phone ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@education_level", item.education_level ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@source", item.source ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@fair_type", item.fair_type ?? (object)DBNull.Value);
                    insertCmd.Parameters.AddWithValue("@created_at", item.created_at);
                    insertCmd.Parameters.AddWithValue("@updated_at", item.updated_at);

                    await insertCmd.ExecuteNonQueryAsync();
                    return Ok("Inserted successfully");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Internal server error: " + ex.Message);
            }
        }
        public class FairRegistration
        {
            public int Id { get; set; }
            public string student_name { get; set; }
            public string student_phone { get; set; }
            public string student_email { get; set; }
            public string residence_city { get; set; }
            public string desired_major { get; set; }
            public string parent_name { get; set; }
            public string parent_phone { get; set; }
            public string education_level { get; set; }
            public string source { get; set; }
            public string fair_type { get; set; }
            public DateTime created_at { get; set; }
            public DateTime updated_at { get; set; }
        }
        public class FairRegistrationResponse
        {
            public string Status { get; set; }
            public List<FairRegistration> Data { get; set; }
        }

        [HttpGet("sync")]
        public async Task<IActionResult> SyncRegistrations()
        {
            try
            {
                await FetchAndInsertFairRegistrationsAsync(); // You can move this to a service if preferred
                return Ok(new { message = "Fair registrations synced successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error syncing registrations", error = ex.Message });
            }
        }

        private async Task FetchAndInsertFairRegistrationsAsync()
        {
            var client = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, "https://acceptedu.com/api/fair-registrations");
            request.Headers.Add("Authorization", "Bearer accept_fair_api_token_2024");
            request.Headers.Add("Accept", "application/json");

            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<FairRegistrationResponse>(json, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            if (result?.Status == "success" && result.Data != null)
            {
                string connectionString = _configuration.GetConnectionString("UEalter");

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    foreach (var item in result.Data)
                    {
                        DateTime created_at = DateTime.TryParse(item.created_at.ToString(), out var validCreated) && validCreated >= new DateTime(1753, 1, 1)
    ? validCreated : (DateTime)SqlDateTime.MinValue;

                        DateTime updated_at = DateTime.TryParse(item.updated_at.ToString(), out var validUpdated) && validUpdated >= new DateTime(1753, 1, 1)
                            ? validUpdated : (DateTime)SqlDateTime.MinValue;
                        var checkCmd = new SqlCommand("SELECT COUNT(*) FROM Agent1Account WHERE student_phone = @Id", connection);
                        checkCmd.Parameters.AddWithValue("@Id", item.student_phone);
                        int count = (int)await checkCmd.ExecuteScalarAsync();

                        if (count == 0)
                        {
                            var insertCmd = new SqlCommand(@"
                            INSERT INTO Agent1Account (
                                Id, student_name, student_phone, student_email, residence_city, desired_major,
                                parent_name, parent_phone, education_level, source, fair_type, created_at, updated_at,  AgencyId, AgencyName,Language
                            ) VALUES (
                                @Id, @student_name, @student_phone, @student_email, @residence_city, @desired_major,
                                @parent_name, @parent_phone, @education_level, @source, @fair_type, @created_at, @updated_at,  @AgencyId, @AgencyName, @Language
                            )", connection);

                            insertCmd.Parameters.AddWithValue("@Id", item.Id);
                            insertCmd.Parameters.AddWithValue("@student_name", item.student_name ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@student_phone", item.student_phone ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@student_email", item.student_email ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@residence_city", item.residence_city ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@desired_major", item.desired_major ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@parent_name", item.parent_name ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@parent_phone", item.parent_phone ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@education_level", item.education_level ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@source", item.source ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@fair_type", item.fair_type ?? (object)DBNull.Value);
                            insertCmd.Parameters.AddWithValue("@created_at", created_at);
                            insertCmd.Parameters.AddWithValue("@updated_at", updated_at);
                            insertCmd.Parameters.AddWithValue("@AgencyId", "001P200000CdO8cIAF");
                            insertCmd.Parameters.AddWithValue("@AgencyName", "Accept Education");
                            insertCmd.Parameters.AddWithValue("@Language", "AR");


                            await insertCmd.ExecuteNonQueryAsync();
                        }
                    }
                }
            }
        }
            
        }
}
