using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Linq;
using System.Text;
using System.Transactions;

namespace StudentManagementAPI.Models
{
    public class TestAttempt
    {
        [Key]
        public int Id { get; set; }

      
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; } // Nullable to indicate if the test is still ongoing

        public bool IsCompleted { get; set; } // Indicates if the test was completed

        public string Status { get; set; } // Current test status: "InProgress", "Paused", "Completed"

        public string? Result { get; set; } // Store the test result

        public string Name { get; set; } // Auto-generated test name
        public string TestType { get; set; } = "full"; // Default to full test type
        public int TotalQuestions { get; set; } // Total number of questions in the test

        public double CompletionPercentage { get; set; } // Percentage of test completion

        public DateTime? LastResumeTime { get; set; } // Track when the test was last resumed

        public ICollection<Answer> Answers { get; set; } // Store answers for this attempt
        public string? SalseId { get; set; }
        // Removed Student navigation property as Student is ignored in TestContext
        // public Student Student { get; set; }
        public string StudentId { get; set; }
    public virtual Student Student { get; set; }
        public TestAttempt()
        {
            StartTime = DateTime.Now;
            IsCompleted = false;
            Status = "InProgress";
            CompletionPercentage = 0;
            Answers = new List<Answer>();
            TestType = "full"; // Initialize with default value
        }
    }
}