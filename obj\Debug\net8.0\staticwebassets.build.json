{"Version": 1, "Hash": "yRpAEi+T7CTpu/0Bq8hxfLxC1q9vDux2T7ZmFVWvvMw=", "Source": "StudentManagementAPI", "BasePath": "_content/StudentManagementAPI", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "StudentManagementAPI\\wwwroot", "Source": "StudentManagementAPI", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\", "BasePath": "_content/StudentManagementAPI", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\0054L000002gKi1QAE.png", "SourceId": "StudentManagementAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\", "BasePath": "_content/StudentManagementAPI", "RelativePath": "uploads/Consultants/0054L000002gKi1QAE#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "njfctx9bt3", "Integrity": "TXVTYKrRH1H7FtM7zLsdnvNJrDL+DYZh7nP8eWgT+c8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Consultants\\0054L000002gKi1QAE.png"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\32e860cd-e60b-49a2-9949-048b010f684e.jpg", "SourceId": "StudentManagementAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\", "BasePath": "_content/StudentManagementAPI", "RelativePath": "uploads/Consultants/32e860cd-e60b-49a2-9949-048b010f684e#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dvqom1z8na", "Integrity": "CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Consultants\\32e860cd-e60b-49a2-9949-048b010f684e.jpg"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\8d467a21-5679-4555-a3cc-a73f2dff33b0.jpg", "SourceId": "StudentManagementAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\", "BasePath": "_content/StudentManagementAPI", "RelativePath": "uploads/Consultants/8d467a21-5679-4555-a3cc-a73f2dff33b0#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dvqom1z8na", "Integrity": "CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\Consultants\\8d467a21-5679-4555-a3cc-a73f2dff33b0.jpg"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.jpg", "SourceId": "StudentManagementAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\", "BasePath": "_content/StudentManagementAPI", "RelativePath": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4iwpq925rx", "Integrity": "NBGfCJ9l9o7lRKht7MUV+wog+QZy7CaZTvOPLM5EZkQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.jpg"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.png", "SourceId": "StudentManagementAPI", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\", "BasePath": "_content/StudentManagementAPI", "RelativePath": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fi10lpacuw", "Integrity": "p+BTY4+D5asfMPiVod27Jf9CKzu2nIHghc9DAnQn3OU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.png"}], "Endpoints": [{"Route": "uploads/Consultants/0054L000002gKi1QAE.njfctx9bt3.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\0054L000002gKi1QAE.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "83965"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"TXVTYKrRH1H7FtM7zLsdnvNJrDL+DYZh7nP8eWgT+c8=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:46:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "njfctx9bt3"}, {"Name": "label", "Value": "uploads/Consultants/0054L000002gKi1QAE.png"}, {"Name": "integrity", "Value": "sha256-TXVTYKrRH1H7FtM7zLsdnvNJrDL+DYZh7nP8eWgT+c8="}]}, {"Route": "uploads/Consultants/0054L000002gKi1QAE.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\0054L000002gKi1QAE.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "83965"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"TXVTYKrRH1H7FtM7zLsdnvNJrDL+DYZh7nP8eWgT+c8=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 09:46:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TXVTYKrRH1H7FtM7zLsdnvNJrDL+DYZh7nP8eWgT+c8="}]}, {"Route": "uploads/Consultants/32e860cd-e60b-49a2-9949-048b010f684e.dvqom1z8na.jpg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\32e860cd-e60b-49a2-9949-048b010f684e.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1076"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Jul 2025 14:20:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dvqom1z8na"}, {"Name": "label", "Value": "uploads/Consultants/32e860cd-e60b-49a2-9949-048b010f684e.jpg"}, {"Name": "integrity", "Value": "sha256-CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM="}]}, {"Route": "uploads/Consultants/32e860cd-e60b-49a2-9949-048b010f684e.jpg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\32e860cd-e60b-49a2-9949-048b010f684e.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1076"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Jul 2025 14:20:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM="}]}, {"Route": "uploads/Consultants/8d467a21-5679-4555-a3cc-a73f2dff33b0.dvqom1z8na.jpg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\8d467a21-5679-4555-a3cc-a73f2dff33b0.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1076"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Jul 2025 14:16:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dvqom1z8na"}, {"Name": "label", "Value": "uploads/Consultants/8d467a21-5679-4555-a3cc-a73f2dff33b0.jpg"}, {"Name": "integrity", "Value": "sha256-CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM="}]}, {"Route": "uploads/Consultants/8d467a21-5679-4555-a3cc-a73f2dff33b0.jpg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\Consultants\\8d467a21-5679-4555-a3cc-a73f2dff33b0.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1076"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 29 Jul 2025 14:16:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CUiwFJd5QaVToRlkhPwqD4BUI7yReVksR/dSF+Xi/KM="}]}, {"Route": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.4iwpq925rx.jpg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58581"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"NBGfCJ9l9o7lRKht7MUV+wog+QZy7CaZTvOPLM5EZkQ=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 11:38:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4iwpq925rx"}, {"Name": "label", "Value": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.jpg"}, {"Name": "integrity", "Value": "sha256-NBGfCJ9l9o7lRKht7MUV+wog+QZy7CaZTvOPLM5EZkQ="}]}, {"Route": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.fi10lpacuw.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "820987"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"p+BTY4+D5asfMPiVod27Jf9CKzu2nIHghc9DAnQn3OU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 01 Jul 2025 11:40:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fi10lpacuw"}, {"Name": "label", "Value": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.png"}, {"Name": "integrity", "Value": "sha256-p+BTY4+D5asfMPiVod27Jf9CKzu2nIHghc9DAnQn3OU="}]}, {"Route": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.jpg", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58581"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"NBGfCJ9l9o7lRKht7MUV+wog+QZy7CaZTvOPLM5EZkQ=\""}, {"Name": "Last-Modified", "Value": "Tu<PERSON>, 01 Jul 2025 11:38:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NBGfCJ9l9o7lRKht7MUV+wog+QZy7CaZTvOPLM5EZkQ="}]}, {"Route": "uploads/students/80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.png", "AssetFile": "C:\\Users\\<USER>\\Desktop\\donet core students\\StudentManagementAPI\\wwwroot\\uploads\\students\\80b6b8c0-5a03-49fd-8ca6-5fcb92d45a83.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "820987"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"p+BTY4+D5asfMPiVod27Jf9CKzu2nIHghc9DAnQn3OU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 01 Jul 2025 11:40:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+BTY4+D5asfMPiVod27Jf9CKzu2nIHghc9DAnQn3OU="}]}]}