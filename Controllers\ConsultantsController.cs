﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.VisualBasic;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace StudentManagementAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ConsultantsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILanguageService _languageService;
        private readonly RsaHelper _uniService1;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly string _connectionString;

        public ConsultantsController(ApplicationDbContext context, ILanguageService languageService, getuni2023 uniService, IHttpClientFactory httpClientFactory, IConfiguration configuration,
            RsaHelper uniService1)

        {
            _connectionString = configuration.GetConnectionString("DefaultConnection")
               ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _languageService = languageService ?? throw new ArgumentNullException(nameof(languageService));
            _uniService = uniService;
            _uniService1 = uniService1;
            _httpClientFactory = httpClientFactory;
            
        }
        private readonly getuni2023 _uniService;

        // GET: api/Consultants
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Consultant>>> GetConsultants([FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                var consultants = await _context.Consultants
                    .Select(c => new
                    {
                        c.Id,
                        c.FirstName,
                        c.LastName,
                        c.PhoneNumber,
                        c.ImageURL,
                        c.DateCreated,
                        c.DateModified,
                        StudentCount = c.Students.Count
                    })
                    .ToListAsync();

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Data = consultants,
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error",
                    Language = lang
                });
            }
        }


        // GET: api/Consultants/ByStudent/{studentId}
        [HttpGet("ByStudent/{studentId}")]
        public async Task<ActionResult<Consultant>> GetConsultantByStudentId(string studentId, [FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                
                var consultant = await _context.Students
                    .Where(s => s.Id == studentId)
                    .Select(s => s.Consultant)
                    .Select(c => new
                    {
                        c.Id,
                        c.FirstName,
                        c.LastName,
                        c.PhoneNumber,
                        c.ImageURL,
                        c.DateCreated,
                        c.DateModified
                    })
                    .FirstOrDefaultAsync();

                if (consultant == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = "No consultant found for the specified student",
                        Language = language
                    });
                }

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Data = consultant,
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error",
                    Language = lang
                });
            }
        }

        // GET: api/Consultants/5
        [HttpGet("{id}")]
        public async Task<ActionResult<Consultant>> GetConsultant(int id, [FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                var consultant = await _context.Consultants
                    .Where(c => c.Id == id)
                    .Select(c => new
                    {
                        c.Id,
                        c.FirstName,
                        c.LastName,
                        c.PhoneNumber,
                        c.ImageURL,
                        c.DateCreated,
                        c.DateModified,
                        Students = c.Students.Select(s => new
                        {
                            s.Id,
                            s.FirstName,
                            s.LastName,
                            s.Email,
                            s.PhoneNumber
                        })
                    })
                    .FirstOrDefaultAsync();

                if (consultant == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("ConsultantNotFound", language) ?? "Consultant not found",
                        Language = language
                    });
                }

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Data = consultant,
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error",
                    Language = lang
                });
            }
        }


        // POST: api/Consultants
        [HttpPost]
        public async Task<ActionResult<Consultant>> CreateConsultant([FromBody] CreateConsultantDto consultantDto, [FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);

                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = _languageService.GetMessage("InvalidRequest", language) ?? "Invalid request",
                        Language = language,
                        Data = ModelState.Values.SelectMany(v => v.Errors)
                    });
                }


                var consultant = new Consultant
                {
                    FirstName = consultantDto.FirstName,
                    LastName = consultantDto.LastName,
                    PhoneNumber = consultantDto.PhoneNumber,
                    ImageURL = consultantDto.ImageURL,
                    DateCreated = DateTime.Now,
                    DateModified = DateTime.Now
                };

                _context.Consultants.Add(consultant);
                await _context.SaveChangesAsync();

                return CreatedAtAction(nameof(GetConsultant), new { id = consultant.Id }, new ApiResponse
                {
                    Status = true,
                    Code = 201,
                    Message = _languageService.GetMessage("ConsultantCreated", language) ?? "Consultant created successfully",
                    Data = new { consultant.Id },
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error",
                    Language = lang
                });
            }
        }
        [AllowAnonymous]
        [HttpPost("upload-image")]
        public async Task<IActionResult> UpdateStudentImage(IFormFile file, [FromQuery] string Id)
        {
            try
            {
                // Validate input parameters
                if (string.IsNullOrEmpty(Id))
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = "Consultant ID is required",
                        Language = "en"
                    });
                }

                // Check if consultant exists - using Any() to avoid loading the entity
                var consultantExists = await _context.Consultants
                    .AnyAsync(e => e.SalesforceId == Id);

                if (!consultantExists)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = "Consultant not found",
                        Language = "en"
                    });
                }

                // Validate file
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = "No file was uploaded",
                        Language = "en"
                    });
                }

                // Validate file size (optional - add max size limit)
                const int maxFileSize = 5 * 1024 * 1024; // 5MB
                if (file.Length > maxFileSize)
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = "File size exceeds maximum limit of 5MB",
                        Language = "en"
                    });
                }

                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var fileExtension = Path.GetExtension(file.FileName)?.ToLowerInvariant();

                if (string.IsNullOrEmpty(fileExtension) || !allowedExtensions.Contains(fileExtension))
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = "Invalid file type. Only JPG, JPEG, PNG, and GIF are allowed.",
                        Language = "en"
                    });
                }

                // Create uploads directory if it doesn't exist
                var uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "Consultants");

                try
                {
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Directory.CreateDirectory(uploadsFolder);
                    }
                }
                catch (Exception ex)
                {
                    return StatusCode(500, new ApiResponse
                    {
                        Status = false,
                        Code = 500,
                        Message = "Failed to create upload directory",
                        Language = "en"
                    });
                }

                // Generate unique filename using consultant ID
                var uniqueFileName = $"{Id}{fileExtension}";
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                // Delete existing file if it exists
                try
                {
                    if (System.IO.File.Exists(filePath))
                    {
                        System.IO.File.Delete(filePath);
                    }
                }
                catch (Exception ex)
                {
                    // Log but don't fail - we can still save the new file
                }

                // Save the file
                try
                {
                    using (var stream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.CopyToAsync(stream);
                    }
                }
                catch (Exception ex)
                {
                    return StatusCode(500, new ApiResponse
                    {
                        Status = false,
                        Code = 500,
                        Message = "Failed to save file",
                        Language = "en"
                    });
                }

                // Update the consultant's image URL using raw SQL to avoid entity loading
                var baseUrl = $"{Request.Scheme}://{Request.Host}";
                var imageUrl = $"{baseUrl}/uploads/Consultants/{uniqueFileName}";

                try
                {
                    // Use raw SQL to update - this avoids loading the entity completely
                    await _context.Database.ExecuteSqlRawAsync(
                        "UPDATE Consultants SET ImageURL = {0}, DateModified = {1} WHERE SalesforceId = {2}",
                        imageUrl, DateTime.Now, Id);
                }
                catch (Exception ex)
                {
                    return StatusCode(500, new ApiResponse
                    {
                        Status = false,
                        Code = 500,
                        Message = "Failed to update database",
                        Language = "en"
                    });
                }

                // Return success response with minimal data
                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Message = "Profile image updated successfully",
                    Data = new
                    {
                        ImageURL = imageUrl,
                        SalesforceId = Id,
                        Message = "Image uploaded and updated successfully"
                    },
                    Language = "en"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error occurred",
                    Language = "en"
                });
            }
        }
        // PUT: api/Consultants/5
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateConsultant(int id, [FromBody] UpdateConsultantDto consultantDto, [FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                
                if (id != consultantDto.Id)
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = _languageService.GetMessage("IdMismatch", language) ?? "ID in URL does not match ID in request body",
                        Language = language
                    });
                }

                var consultant = await _context.Consultants.FindAsync(id);
                if (consultant == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("ConsultantNotFound", language) ?? "Consultant not found",
                        Language = language
                    });
                }


                // Update only the provided fields
                consultant.FirstName = consultantDto.FirstName ?? consultant.FirstName;
                consultant.LastName = consultantDto.LastName ?? consultant.LastName;
                consultant.PhoneNumber = consultantDto.PhoneNumber ?? consultant.PhoneNumber;
                consultant.ImageURL = consultantDto.ImageURL ?? consultant.ImageURL;
                consultant.DateModified = DateTime.Now;

                _context.Entry(consultant).State = EntityState.Modified;

                try
                {
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ConsultantExists(id))
                    {
                        return NotFound(new ApiResponse
                        {
                            Status = false,
                            Code = 404,
                            Message = _languageService.GetMessage("ConsultantNotFound", language) ?? "Consultant not found",
                            Language = language
                        });
                    }
                    else
                    {
                        throw;
                    }
                }

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Message = _languageService.GetMessage("ConsultantUpdated", language) ?? "Consultant updated successfully",
                    Data = new { consultant.Id },
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error",
                    Language = lang
                });
            }
        }

        // DELETE: api/Consultants/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteConsultant(int id, [FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                var consultant = await _context.Consultants.FindAsync(id);
                if (consultant == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("ConsultantNotFound", language) ?? "Consultant not found",
                        Language = language
                    });
                }

                // Check if consultant has any students assigned
                var hasStudents = await _context.Students.AnyAsync(s => s.ConsultantId == id);
                if (hasStudents)
                {
                    return BadRequest(new ApiResponse
                    {
                        Status = false,
                        Code = 400,
                        Message = _languageService.GetMessage("CannotDeleteConsultantWithStudents", language) ?? "Cannot delete consultant with assigned students",
                        Language = language
                    });
                }

                _context.Consultants.Remove(consultant);
                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Message = _languageService.GetMessage("ConsultantDeleted", language) ?? "Consultant deleted successfully",
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "Internal server error",
                    Language = lang
                });
            }
        }
        private async Task<string> SaveBase64ImageToFile(string base64Data, string fileName)
        {
            // Decode the base64 string
            byte[] imageBytes = Convert.FromBase64String(base64Data);

            // Define the folder where images will be saved
            string uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot/uploads/Consultants");

            // Ensure the folder exists
            Directory.CreateDirectory(uploadsFolder);

            // Generate a unique filename (e.g., using GUID)
            string filePath = Path.Combine(uploadsFolder, $"{fileName}.jpg");

            // Save the image to disk
            using (FileStream fs = new FileStream(filePath, FileMode.Create))
            {
                await fs.WriteAsync(imageBytes, 0, imageBytes.Length);
            }

            // Return the relative URL of the saved image
            string imageUrl = $"/uploads/Consultants/{fileName}.jpg";
            return imageUrl;
        }
        /// <summary>
        /// Request model for updating a consultant.
        /// Matches the [Consultants] table fields in the database.
        /// </summary>
        public class UpdateConsultantRequest
        {
            public string Id { get; set; }

            public string FirstName { get; set; }

            public string LastName { get; set; }

            public string MobilePhone { get; set; }

            public string FullPhotoUrl { get; set; }  // Original Salesforce image URL

            public string AboutMe { get; set; }

            public string Email { get; set; }

            // Add other fields if needed (e.g., SalesforceId, etc.)
            // public string SalesforceId { get; set; }
        }
        private async Task UpdateConsultantInDatabase(int id,UpdateConsultantRequest req, bool updateImageUrl = false, string newImageUrl = null)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            var sql = @"
        UPDATE [Consultants] SET 
            FirstName = @FirstName,
            LastName = @LastName,
            PhoneNumber = @PhoneNumber,
            Description = @Description,
            Email = @Email";

            if (updateImageUrl)
            {
                sql += ", ImageURL = @NewImageUrl";
            }

            sql += @"
        WHERE Id = @Id";

            using var cmd = new SqlCommand(sql, connection);
            cmd.Parameters.AddWithValue("@FirstName", req.FirstName ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@LastName", req.LastName ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@PhoneNumber", req.MobilePhone ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@Description", req.AboutMe ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@Email", req.Email ?? (object)DBNull.Value);
            cmd.Parameters.AddWithValue("@Id",id);

            if (updateImageUrl)
            {
                cmd.Parameters.AddWithValue("@NewImageUrl", newImageUrl);
            }

            await cmd.ExecuteNonQueryAsync();
        }
        private async Task<string> FetchBase64FromSalesforce(string imageUrl)
        {
            // Step 1: Get a valid Salesforce access token
            var accessToken = _uniService1.GetAccessToken();
            if (string.IsNullOrWhiteSpace(accessToken))
            {
                throw new Exception("Salesforce access token is null or empty. Authentication required.");
            }

            // Step 2: Define endpoint (trim extra spaces in URL)
            var salesforceUrl = "https://vaha.my.salesforce.com/services/apexrest/UserPhotoExtractor/v1/";

            // Step 3: Prepare request payload
            var payload = new { fullPhotoUrl = imageUrl };
            var jsonContent = new StringContent(
                JsonSerializer.Serialize(payload),
                Encoding.UTF8,
                "application/json"
            );

            // Step 4: Use HttpClient from factory
            var client = _httpClientFactory.CreateClient();
            client.DefaultRequestHeaders.Clear();
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            try
            {
                var response = await client.PostAsync(salesforceUrl, jsonContent);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"Salesforce API error: {response.StatusCode} - {errorContent}");
                }

                // ✅ Key Fix: Deserialize to Dictionary<string, JsonElement> to use .GetString()
                using var doc = await JsonDocument.ParseAsync(await response.Content.ReadAsStreamAsync());
                var root = doc.RootElement;

                if (root.TryGetProperty("success", out var success) && success.GetBoolean() &&
                    root.TryGetProperty("base64Data", out var base64Element))
                {
                    return base64Element.GetString(); // ✅ Now valid
                }

                var errorMsg = root.TryGetProperty("errorMessage", out var msg) ? msg.GetString() : "Unknown error";
                throw new Exception($"Salesforce response failed: {errorMsg}");
            }
            catch (HttpRequestException httpEx)
            {
                throw new Exception("Network error while calling Salesforce API.", httpEx);
            }
            catch (TaskCanceledException timeoutEx)
            {
                throw new Exception("Request to Salesforce timed out.", timeoutEx);
            }
        }
        [AllowAnonymous]
        [HttpPut("update-user")]
        public async Task<IActionResult> UpdateConsultant( [FromBody] UpdateConsultantRequest request)
        {

            int id = _context.Consultants.Where(c => c.SalesforceId == request.Id).Select(c => c.Id)
                               .FirstOrDefault();
            try
            {
                
                // Step 1: Update local DB (without ImageURL yet)
                await UpdateConsultantInDatabase(id,request, false); // Pass false to skip updating ImageURL

                string newImageUrl = null;

                // Step 2: If ImageURL is provided, fetch base64 from Salesforce
                if (!string.IsNullOrWhiteSpace(request.FullPhotoUrl))
                {
                    string base64Image = await FetchBase64FromSalesforce(request.FullPhotoUrl);

                    // Step 3: Save the base64 image to a file
                    string fileName = Guid.NewGuid().ToString(); // Use GUID for unique filenames
                    newImageUrl = await SaveBase64ImageToFile(base64Image, fileName);
                    var baseUrl = $"{Request.Scheme}://{Request.Host}";
                    newImageUrl = $"{baseUrl}" + newImageUrl;
                    // Step 4: Update the ImageURL in the database
                    await UpdateConsultantInDatabase(id,request, true, newImageUrl);
                }

                return Ok(new
                {
                    Message = "Consultant updated",
                    NewImageUrl = newImageUrl
                });
            }
            catch (Exception ex)
            {
                // Log the exception
                return StatusCode(500, new { Error = "An error occurred while updating the consultant", Details = ex.Message });
            }
        }
        private bool ConsultantExists(int id)
        {
            return _context.Consultants.Any(e => e.Id == id);
        }
    }

    public class CreateConsultantDto
    {
        [Required(ErrorMessage = "FirstName is required")]
        [StringLength(100, ErrorMessage = "FirstName cannot be longer than 100 characters")]
        public string FirstName { get; set; }

        [Required(ErrorMessage = "LastName is required")]
        [StringLength(100, ErrorMessage = "LastName cannot be longer than 100 characters")]
        public string LastName { get; set; }

        [StringLength(20, ErrorMessage = "PhoneNumber cannot be longer than 20 characters")]
        public string PhoneNumber { get; set; }

        public string ImageURL { get; set; }
    }

    public class UpdateConsultantDto
    {
        public int Id { get; set; }

        [StringLength(100, ErrorMessage = "FirstName cannot be longer than 100 characters")]
        public string FirstName { get; set; }

        [StringLength(100, ErrorMessage = "LastName cannot be longer than 100 characters")]
        public string LastName { get; set; }

        [StringLength(20, ErrorMessage = "PhoneNumber cannot be longer than 20 characters")]
        public string PhoneNumber { get; set; }

        public string ImageURL { get; set; }
    }
}
