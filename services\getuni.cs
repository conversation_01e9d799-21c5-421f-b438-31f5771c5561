using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Data.SqlClient;
using System.Data.SqlClient;
using System.Data.SqlClient;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;
using static StudentManagementAPI.Controllers.ApplicationController;
using static UniversityProgramsController;

namespace StudentManagementAPI.service
{
    public class getuni2023
    {
     
        private readonly RsaHelper _uniService;
        private readonly sit _context;
        private readonly IOptions<SalesforceSettings> _salesforceSettings;
        private readonly IHttpContextAccessor _httpContextAccessor;

 

        public getuni2023(RsaHelper uniService, sit context, IOptions<SalesforceSettings> salesforceSettings, IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
        {
           
            _uniService = uniService;
            _context = context;
            _salesforceSettings = salesforceSettings;
            _httpContextAccessor = httpContextAccessor;
        }

        public string invoicestatus(string leadId)
        {

            //prepare the query
            string query = "SELECT Status__c    FROM Invoice__c  WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            // Check the 'totalSize' or if 'records' is empty
            if ((int)json["totalSize"] > 0 && json["records"] is JArray records && records.Count > 0)
            {
                // Process the first record
                string value = (string)records[0]["Status__c"];
                return value;
            }
            else
            {
                // Handle empty results
                Console.WriteLine("No records found.");
                return "No records available"; // Or throw an exception if needed
            }
        }
        public async Task<string> countappAsync(string contentDocumentId)
        {
           
            string salceidtoken = _uniService.GetAccessToken();
            var client = new HttpClient();
            var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, "https://vaha.my.salesforce.com/services/apexrest/GetBase64/v1/");
            request.Headers.Add("Authorization", "Bearer " + salceidtoken);

            var content = new StringContent("{\r\n    \"contentDocumentId\" : \"" + contentDocumentId + "\"}", null, "application/json");
            request.Content = content;
            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();
            string yy = await response.Content.ReadAsStringAsync();
            return yy;
        }
        public string invoicestatusreson(string leadId)
        {

            //prepare the query
            string query = "SELECT Rejection_Reason__c    FROM Invoice__c  WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            // Check the 'totalSize' or if 'records' is empty
            if ((int)json["totalSize"] > 0 && json["records"] is JArray records && records.Count > 0)
            {
                // Process the first record
                string value = (string)records[0]["Rejection_Reason__c"];
                return value;
            }
            else
            {
                // Handle empty results
                Console.WriteLine("No records found.");
                return "No records available"; // Or throw an exception if needed
            }
        }
        public string Deposit_Invoice__c(string leadId)
        {

            //prepare the query
            string query = "SELECT Deposit_Invoice__c FROM Application__c   WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Deposit_Invoice__c"];

            return value;
        }
        public string contactidfromapp(string leadId)
        {

            //prepare the query
            string query = "SELECT Applicant_PA__c FROM Application__c   WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Applicant_PA__c"];

            return value;
        }
        public string Offer_Letter_Link__c(string leadId)
        {

            //prepare the query
            string query = "SELECT Offer_Letter_Link__c FROM Application__c   WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Offer_Letter_Link__c"];

            return value;
        }
        public string Acceptance_Letter_URL__c(string leadId)
        {

            //prepare the query
            string query = "SELECT Acceptance_Letter_URL__c FROM Application__c   WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Acceptance_Letter_URL__c"];

            return value;
        }
        public string accountId(string leadId)
        {

            //prepare the query
            string query = "SELECT Id   FROM Account WHERE  PersonContactId= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Id"];

            return value;
        }
        public string uninamesalce(string leadId)
        {

            //prepare the query
            string query = "SELECT Name   FROM Account WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Name"];

            return value;
        }
        public string countryofres(string leadId)
        {

            //prepare the query
            string query = "SELECT Country_of_Residence__pc   FROM Account WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Country_of_Residence__pc"];

            return value;
        }
        public string ExtractHrefLink(string html)
        {
            // Check if the input HTML is null or empty
            if (string.IsNullOrEmpty(html))
            {
                return ""; // Return empty string for null or empty input
            }

            // Define a regex pattern to match the href attribute in an <a> tag
            string pattern = @"<a[^>]*\s+href\s*=\s*[""']([^""']+)[""'][^>]*>(.*?)<\/a>";

            // Find matches
            MatchCollection matches = Regex.Matches(html, pattern, RegexOptions.IgnoreCase);

            // Check if any matches were found
            if (matches.Count > 0)
            {
                foreach (Match match in matches)
                {
                    // Extract the href value
                    string hrefValue = match.Groups[1].Value;
                    Console.WriteLine(hrefValue); // Output the href value
                    return hrefValue; // Return the first href value found
                }
            }

            // If no <a> tags with href found, return an empty string or the original html
            return html; // or return html if you prefer to return it in case of no href found
        }
    

    public string passport(string leadId)
        {

            //prepare the query
            string query = "SELECT Passport__c   FROM contact WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["Passport__c"];

            return value;
        }
        public string companyphone(string leadId)
        {

            //prepare the query
            string query = "SELECT PersonContactId   FROM Account WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["PersonContactId"];

            return value;
        }
   
        public  string GetFileExtensionFromUrl(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                throw new ArgumentException("The URL is null or empty");
            }

            try
            {
                // Create a Uri object from the URL
                Uri uri = new Uri(url);

                // Get the path part of the URL
                string path = uri.AbsolutePath;

                // Find the last '.' in the path to get the file extension
                int lastDotIndex = path.LastIndexOf('.');

                if (lastDotIndex == -1 || lastDotIndex == path.Length - 1)
                {
                    // No file extension found
                    return string.Empty;
                }

                // Extract the file extension
                string extension = path.Substring(lastDotIndex + 1).ToLowerInvariant();
                return extension;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to extract file extension from URL", ex);
            }
        }
        public async Task<string> ConvertImageUrlToBase64Async(string imageUrl)
        {
            if (string.IsNullOrEmpty(imageUrl))
            {
                throw new ArgumentException("The image URL is null or empty");
            }

            using (var httpClient = new HttpClient())
            {
                // Download the image data from the URL
                byte[] imageBytes = await httpClient.GetByteArrayAsync(imageUrl);

                // Convert the image data to a Base64 string
                return Convert.ToBase64String(imageBytes);
            }
        }
       
        public string Applicant_Name__c(string leadId)
        {

            //prepare the query
            string query = "SELECT Applicant_Name__c   FROM Application__c WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            if (json["records"] != null && json["records"].HasValues)
            {
                string value = (string)json["records"][0]["Applicant_Name__c"];

            return value;
            }
            else
            {
                // Handle the case where "records" is empty or null
                return "No records found";
            }
        }
        public string Program_Name__c(string leadId)
        {

            //prepare the query
            string query = "SELECT Program_Name__c   FROM Application__c WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            if (json["records"] != null && json["records"].HasValues)
            {
                string value = (string)json["records"][0]["Program_Name__c"];

            return value;
            }
            else
            {
                // Handle the case where "records" is empty or null
                return "No records found";
            }
        }
        public string Stage__c(string leadId)
        {

            //prepare the query
            string query = "SELECT Stage__c   FROM Application__c WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            if (json["records"] != null && json["records"].HasValues)
            {
                string value = (string)json["records"][0]["Stage__c"];

            return value;
            }
            else
            {
                // Handle the case where "records" is empty or null
                return "No records found";
            }
        }
        public string Nameapp(string leadId)
        {

            //prepare the query
            string query = "SELECT Name   FROM Application__c WHERE  Id= '" + leadId + "'";

            //Query
            var result = Query(query);

            JObject json = JObject.Parse(result.Result);
            if (json["records"] != null && json["records"].HasValues)
            {
                string value = (string)json["records"][0]["Name"];

                return value;
            }
            else
            {
                // Handle the case where "records" is empty or null
                return "No records found";
            }
        }
      


        public class userlist10
        {
            public string stname;
            public string id;
            public string fstate;
            public string state;
       
        }
        public class sponcer
        {
            public int sid;
            public int sarrange;
            public int squnt;
            public string color;


        }
        public class lect
        {
            public string sid;
      
            public int squnt;
            public string name;



        }

        public void LogApiRequest(string method, string path, string queryString, string body, string ipAddress)
        {
            try
            {
                // Validate and sanitize input data
                var log = new ApiRequestLognewapi
                {
                    // Don't set Id - let it auto-increment
                    Method = SanitizeString(method, 50, "UNKNOWN"),
                    Path = SanitizeString(path, int.MaxValue, "/"),
                    QueryString = SanitizeString(queryString, int.MaxValue, string.Empty),
                    Body = SanitizeString(body, int.MaxValue, string.Empty),
                    Timestamp = DateTime.UtcNow,
                    IpAddress = SanitizeString(ipAddress, 100, "0.0.0.0")
                };

                // Add to context and save
                _context.apiRequestLognewapis.Add(log);
                _context.SaveChanges();
            }
            catch (DbUpdateException ex)
            {
                // Handle database-specific errors
                var innerMessage = ex.InnerException?.Message ?? ex.Message;
                var errorMessage = $"Database error while logging API request: {innerMessage}";

                // Log the error (replace with your logging mechanism)
                Console.WriteLine($"ERROR: {errorMessage}");

                // Optionally re-throw or handle silently depending on your requirements
                // throw new Exception(errorMessage, ex);
            }
            catch (InvalidOperationException ex)
            {
                // Handle context or connection issues
                var errorMessage = $"Database context error: {ex.Message}";
                Console.WriteLine($"ERROR: {errorMessage}");

                // throw new Exception(errorMessage, ex);
            }
            catch (Exception ex)
            {
                // Handle any other unexpected errors
                var errorMessage = $"Unexpected error while logging API request: {ex.Message}";
                Console.WriteLine($"ERROR: {errorMessage}");

                // throw new Exception(errorMessage, ex);
            }
        }


        private string SanitizeString(string input, int maxLength, string defaultValue)
        {
            // Handle null or empty input
            if (string.IsNullOrEmpty(input))
            {
                return defaultValue;
            }

            // Trim whitespace
            input = input.Trim();

            // Handle empty after trim
            if (string.IsNullOrEmpty(input))
            {
                return defaultValue;
            }

            // Truncate if necessary
            if (input.Length > maxLength)
            {
                input = input.Substring(0, maxLength);
            }

            return input;
        }


        public async Task<string> Insert(string sobject, object body)
        {
         
            string salceidtoken = _uniService.GetAccessToken();
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            var request1 = WebRequest.Create($"{_salesforceSettings.Value.BaseUrl}/sobjects/{sobject}");
            request1.Method = "POST";
            request1.Headers.Add("Authorization", "Bearer " + salceidtoken);
            request1.ContentType = "application/json";

            var json = JsonConvert.SerializeObject(body);
            var byteData = Encoding.UTF8.GetBytes(json);
            request1.ContentLength = byteData.Length;

            using (var stream1 = request1.GetRequestStream())
            {
                stream1.Write(byteData, 0, byteData.Length);
            }
            try
            {
                var response1 = request1.GetResponse();
                var streamReader = new StreamReader(response1.GetResponseStream());
                var responseContent = streamReader.ReadToEnd();
                var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();

                LogApiRequest("Insert", "Insert", "", json, ipAddress);

                return responseContent;
               
            }
            catch (WebException ex)
            {
                string error = ex.Message;
                // يمكنك إدراج معالجة خاصة للأخطاء هنا
                // على سبيل المثال، يمكنك استخدام ex.Response للوصول إلى رمز الحالة والاستجابة المتعلقة
            
                throw; // إعادة الاستثناء أو التعامل معه بأي شكل من الأشكال
            }

        }
     
        public async Task<string> Update(string sobject, string recordId, object body)
        {
            string salceidtoken = _uniService.GetAccessToken();

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            var request1 = WebRequest.Create($"{_salesforceSettings.Value.BaseUrl}/sobjects/{sobject}/{recordId}");
            request1.Method = "PATCH";
            request1.Headers.Add("Authorization", "Bearer " + salceidtoken);
            request1.ContentType = "application/json";

            var json = JsonConvert.SerializeObject(body);
            var byteData = Encoding.UTF8.GetBytes(json);
            request1.ContentLength = byteData.Length;

            using (var stream1 = request1.GetRequestStream())
            {
                stream1.Write(byteData, 0, byteData.Length);
            }
            try
            {
                var response1 = request1.GetResponse();
                var streamReader = new StreamReader(response1.GetResponseStream());
                var responseContent = streamReader.ReadToEnd();
                var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();

                LogApiRequest("Update", "Update", recordId, json, ipAddress);

                return responseContent;

            }
            catch (WebException ex)
            {
                string error = ex.Message;
                // يمكنك إدراج معالجة خاصة للأخطاء هنا
                // على سبيل المثال، يمكنك استخدام ex.Response للوصول إلى رمز الحالة والاستجابة المتعلقة
             
                throw; // إعادة الاستثناء أو التعامل معه بأي شكل من الأشكال
            }

        }
   
        public  string hashpassword(string link)
        {
            string salceidtoken = _uniService.GetAccessToken();
            var client = new HttpClient();
            var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, $"{_salesforceSettings.Value.ApexRestBaseUrl}/HashPassword/v1/");
            request.Headers.Add("Authorization", "Bearer " + salceidtoken);
        

            var content = new StringContent("{\r\n    \"password\": \"" + link + "\"}", null, "application/json");
            request.Content = content;

            var response = client.SendAsync(request).Result; // Blocking call to wait for the task to complete
            response.EnsureSuccessStatusCode(); // Now response is HttpResponseMessage, so this will work

            var result = response.Content.ReadAsStringAsync().Result; // Blocking call to wait for the task to complete
                                                                      // Remove first and last characters
            if (result.Length > 2)
            {
                result = result.Substring(1, result.Length - 2);
            }
            else
            {
                result = string.Empty; // Handle short responses appropriately
            }
            var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();

            LogApiRequest("hashpassword", "hashpassword", "recordId", "json", ipAddress);

            return result;
        }
   
        public string Countryen(string code)
        {

            var countryToUpdate = _context.clists.FirstOrDefault(c => c.clistid == code);
            return countryToUpdate?.name ?? "Not found";
        }
        public class Attributes
        {
            public string type { get; set; }
            public string url { get; set; }
        }
        public class Recordlogin
        {
            public Attributes attributes { get; set; }
            public string Id { get; set; }
            public DateTime? CreatedDate { get; set; }
            public int? Maximum_Application_Limit { get; set; } = 10;
                
            public string Name { get; set; }
            public string Password { get; set; }
            public string PhotoUrl { get; set; }
            public string Status { get; set; }
            public string Username { get; set; }
            public string Website { get; set; }
            public string Tax_ID { get; set; }
            public string Phone { get; set; }
            public string Instagram { get; set; }
            public string Address { get; set; }
            public string RecordTypeId { get; set; }
            public string University_Logo_URL { get; set; }
            public string Authorized_First_Name { get; set; }
            public string Authorized_Last_Name { get; set; }
        }

        public class Rootlogin
        {
            public int totalSize { get; set; }
            public List<Recordlogin> records { get; set; }
            public bool done { get; set; }
            public bool IsExist { get; set; }
        }

        public async Task<Rootlist2> after2(string link)
        {

            string salceidtoken = _uniService.GetAccessToken();
            var client = new HttpClient();
            var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Get, $"https://{_salesforceSettings.Value.Domain}{link}");
            request.Headers.Add("Authorization", "Bearer " + salceidtoken);

            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadAsStringAsync();
            Rootlist2 json1 = JsonConvert.DeserializeObject<Rootlist2>(result);
            return json1;
        }
        public async Task<string> Query(string sqlQuery)
        {
            string salceidtoken = _uniService.GetAccessToken();

            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            sqlQuery = sqlQuery.Replace("'s", "\\'s");
            var request = WebRequest.Create($"{_salesforceSettings.Value.BaseUrl}/query?q={Uri.EscapeDataString(sqlQuery)}");
            request.Method = "GET";
            request.Headers.Add("Authorization", "Bearer " + salceidtoken);
            try
            {
                var response = request.GetResponse();
                var stream = response.GetResponseStream();
                var reader = new StreamReader(stream);
                var result = reader.ReadToEnd();
                var ipAddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();

                LogApiRequest("Query", "Query", sqlQuery, "", ipAddress);

                return result;
            }
            catch (WebException ex)
            {
                string error = ex.Message;
                // يمكنك إدراج معالجة خاصة للأخطاء هنا
                // على سبيل المثال، يمكنك استخدام ex.Response للوصول إلى رمز الحالة والاستجابة المتعلقة

                throw; // إعادة الاستثناء أو التعامل معه بأي شكل من الأشكال
            }

        }
        public async Task<string> updatepassword(string recordid, string Password__c)
        {


            string sObject = "Account";
            object body = new object();


            body = new
            {


                Password__pc = Password__c

            };



            //update
            return await Update(sObject, recordid, body);
        }

        public int totalaccountwithappgen(string leadId, string subagentid, string empid, string year = "All", string semster = "0", string? gen = "0")
        {
            //string query = "";

            ////prepare the query Sub_Agency_Id__c!='0'

            //query = "SELECT Count(Id)   FROM Account WHERE  Agency__pc= '" + leadId + "' and Has_an_application__pc=true";
            //if (subagentid != "0" && subagentid != "1")
            //{
            //    query = "SELECT Count(Id)   FROM Account WHERE  Agency__pc= '" + leadId + "' and Sub_Agency_Id__pc= '" + subagentid + "'   and Has_an_application__pc=true";

            //}
            //if (empid != "0")
            //{
            //    query = "SELECT Count(Id)   FROM Account WHERE  Agency__pc= '" + leadId + "' and Agency_Employee_Id__pc= '" + empid + "'   and Has_an_application__pc=true";


            //}
            //if (year != "All")
            //{
            //    query += " and Preferred_Academic_Year__pc='" + year + "'";
            //}
            //if (semster != "0")
            //{
            //    query += " and Preferred_Semester__pc='" + semster + "'";
            //}
            //if (gen != "0")
            //{
            //    query += " and PersonGenderIdentity='" + gen + "'";
            //}
            //if (subagentid == "1")
            //{
            //    query += " and Sub_Agency_Id__pc!=null";
            //}
            ////Query
            //var result = Query(query);

            //JObject json = JObject.Parse(result.Result);
            //int value = (Int16)json["records"][0]["expr0"];

            //return value;
            return 0;
        }
        public int totalaccountwithappnat(string leadId, string subagentid, string empid, string year = "All", string semster = "0", string? gen = "0", string? sec = "0")
        {
            //string query = "";

            ////prepare the query Sub_Agency_Id__c!='0'

            //query = "SELECT Count(Id)   FROM Account WHERE  Agency__pc= '" + leadId + "' and Has_an_application__pc=true";
            //if (subagentid != "0" && subagentid != "1")
            //{
            //    query = "SELECT Count(Id)   FROM Account WHERE  Agency__pc= '" + leadId + "' and Sub_Agency_Id__pc= '" + subagentid + "'   and Has_an_application__pc=true";

            //}
            //if (empid != "0")
            //{
            //    query = "SELECT Count(Id)   FROM Account WHERE  Agency__pc= '" + leadId + "' and Agency_Employee_Id__pc= '" + empid + "'   and Has_an_application__pc=true";


            //}
            //if (year != "All")
            //{
            //    query += " and Preferred_Academic_Year__pc='" + year + "'";
            //}
            //if (semster != "0")
            //{
            //    query += " and Preferred_Semester__pc='" + semster + "'";
            //}
            //if (gen != "0")
            //{
            //    query += " and Citizenship__pc='" + gen + "'";
            //}
            //if (sec != "0")
            //{
            //    query += " and Country_of_Secondary_School__pc='" + sec + "'";
            //}

            //if (subagentid == "1")
            //{
            //    query += " and Sub_Agency_Id__pc!=null";
            //}
            ////Query
            //var result = Query(query);

            //JObject json = JObject.Parse(result.Result);
            //int value = (Int16)json["records"][0]["expr0"];

            //return value;
            return 0;
        }
      
        public string FriendlyURLTitle(string incomingText)
        {

            if (incomingText != null)
            {
                incomingText = incomingText.Replace("ş", "s");
                incomingText = incomingText.Replace("Ş", "s");
                incomingText = incomingText.Replace("İ", "i");
                incomingText = incomingText.Replace("I", "i");
                incomingText = incomingText.Replace("ı", "i");
                incomingText = incomingText.Replace("ö", "o");
                incomingText = incomingText.Replace("Ö", "o");
                incomingText = incomingText.Replace("ü", "u");
                incomingText = incomingText.Replace("Ü", "u");
                incomingText = incomingText.Replace("Ç", "c");
                incomingText = incomingText.Replace("ç", "c");
                incomingText = incomingText.Replace("ğ", "g");
                incomingText = incomingText.Replace("Ğ", "g");

                incomingText = incomingText.Replace("---", "-");
                incomingText = incomingText.Replace("?", "");








                string encodedUrl = incomingText;
                // & ile " " yer değiştirme
                encodedUrl = Regex.Replace(encodedUrl, @"\&+", "and");
                // " " karakterlerini silme

                // geçersiz karakterleri sil

                // tekrar edenleri sil
                encodedUrl = Regex.Replace(encodedUrl, @"-+", "-");

                return encodedUrl;
            }
            else
            {
                return "";
            }
        }
      

   
        public string updatevalue(string lang, string value, string oldvalueljson)
        {
            if (oldvalueljson.Contains("#"))
            {
                StringBuilder sb = new StringBuilder(oldvalueljson);
                sb.Replace("{#ar#:#", "{\"ar\":\"");
                sb.Replace("#,#en#:#", "\",\"en\":\"");
                sb.Replace("#}", "\"}");
                sb.Replace("#", "\"");
                oldvalueljson = sb.ToString();
            }
            string value1 = FriendlyURLTitle(value);

            JObject obj = JObject.Parse(oldvalueljson);
            var val = obj;

            if (lang == "ar")
            {
                val["ar"] = value1;
            }
            else if (lang == "en")
            {
                val["en"] = value1;
            }
            else
            {
                val["tr"] = value1;

            }
            string result = JsonConvert.SerializeObject(obj);
            return result;
        }
        public string getvalue(string lang, string oldvalueljson)
        {
            if (oldvalueljson != null)
            {
                if (oldvalueljson.Contains("#"))
                {
                    StringBuilder sb = new StringBuilder(oldvalueljson);
                    sb.Replace("{#ar#:#", "{\"ar\":\"");
                    sb.Replace("#,#en#:#", "\",\"en\":\"");
                    sb.Replace("#}", "\"}");
                    sb.Replace(",", "@");
                    sb.Replace("\\#", "\'}");
                    sb.Replace("color: \"", "color: #");
                    oldvalueljson = sb.ToString();
                }

                JObject obj = JObject.Parse(oldvalueljson);
                var val = obj;
                string result = "";
                if (lang == "ar")
                {
                    result = val["ar"].ToString();
                }
                else if (lang == "en")
                {
                    result = val["en"].ToString();
                }
                else
                {
                    result = val["tr"].ToString();

                }
                string lang2 = "";
                if (lang == "ar") { lang2 = "en"; }
                else
                {
                    lang2 = "ar";
                }
                string value = "";
                if (result != "")
                {
                    value = result;
                }
                else { value = val[lang2].ToString(); }
                return value;
            }
            else
            {
                return "ok";
            }

        }
    }
    
}