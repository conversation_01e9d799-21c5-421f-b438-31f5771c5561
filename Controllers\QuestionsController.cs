using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;

namespace StudentManagementAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class QuestionsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public QuestionsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/Questions?languages=en,ar,ru,fa&testAttemptId=1&testType=small
        [HttpGet]
        public async Task<ActionResult<IEnumerable<object>>> GetQuestions([FromQuery] string languages, [FromQuery] int? testAttemptId, [FromQuery] string testType = "full")
        {
            try
            {
                var questions = await _context.Questions
                    .Include(q => q.Answers.Where(a => !testAttemptId.HasValue || a.TestAttemptId == testAttemptId.Value))
                    .ToListAsync();
                
                // Filter questions based on test type
                if (testType.ToLower() == "small")
                {
                    // For small test, select 10 questions per personality type
                    var personalityTypes = questions.Select(q => q.PersonalityTypeId).Distinct().ToList();
                    var filteredQuestions = new List<Question>();

                    foreach (var typeId in personalityTypes)
                    {
                        var questionsOfType = questions.Where(q => q.PersonalityTypeId == typeId).Take(10).ToList();
                        filteredQuestions.AddRange(questionsOfType);
                    }

                    questions = filteredQuestions;
                }

                var requestedLanguages = string.IsNullOrEmpty(languages) ? new[] { "en" } : languages.ToLower().Split(',');

                var result = questions.Select(q => new
                {
                    q.Id,
                    q.PersonalityTypeId,
                    q.OrderNumber,
                    q.IsActive,
                    Text = new Dictionary<string, string>
                    {
                        { "en", requestedLanguages.Contains("en") ? q.TextEn : null },
                        { "ar", requestedLanguages.Contains("ar") ? q.TextAr : null },
                        { "ru", requestedLanguages.Contains("ru") ? q.TextRu : null },
                        { "fa", requestedLanguages.Contains("fa") ? q.TextFa : null }
                    }.Where(kvp => kvp.Value != null).ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    Answer = testAttemptId.HasValue && q.Answers.Any() ? q.Answers.First().Response : null
                });

                return Ok(result);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // GET: api/Questions/{id}?languages=en,ar,ru,fa
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetQuestion(int id, [FromQuery] string languages)
        {
            try
            {
                var question = await _context.Questions
                    .FirstOrDefaultAsync(q => q.Id == id);

                if (question == null)
                {
                    return NotFound();
                }

                if (string.IsNullOrEmpty(languages))
                {
                    return question;
                }

                var requestedLanguages = languages.ToLower().Split(',');
                var result = new
                {
                    question.Id,
                    question.PersonalityTypeId,
                    question.OrderNumber,
                    question.IsActive,
                    Text = new Dictionary<string, string>
                    {
                        { "en", requestedLanguages.Contains("en") ? question.TextEn : null },
                        { "ar", requestedLanguages.Contains("ar") ? question.TextAr : null },
                        { "ru", requestedLanguages.Contains("ru") ? question.TextRu : null },
                        { "fa", requestedLanguages.Contains("fa") ? question.TextFa : null }
                    }.Where(kvp => kvp.Value != null).ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                };

                return Ok(result);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // GET: api/Questions/active?languages=en,ar,ru,fa&testAttemptId=1&testType=small
        [HttpGet("active")]
        public async Task<ActionResult<IEnumerable<object>>> GetActiveQuestions([FromQuery] string languages, [FromQuery] int? testAttemptId, [FromQuery] string testType = "full")
        {
            try
            {
                var questions = await _context.Questions
                    .Where(q => q.IsActive)
                    .Include(q => q.Answers.Where(a => !testAttemptId.HasValue || a.TestAttemptId == testAttemptId.Value))
                    .OrderBy(q => q.OrderNumber)
                    .ToListAsync();

                // Filter questions based on test type
                if (testType.ToLower() == "small")
                {
                    // For small test, select 10 questions per personality type
                    var personalityTypes = questions.Select(q => q.PersonalityTypeId).Distinct().ToList();
                    var filteredQuestions = new List<Question>();

                    foreach (var typeId in personalityTypes)
                    {
                        var questionsOfType = questions.Where(q => q.PersonalityTypeId == typeId).Take(10).ToList();
                        filteredQuestions.AddRange(questionsOfType);
                    }

                    questions = filteredQuestions;
                }

                var requestedLanguages = string.IsNullOrEmpty(languages) ? new[] { "en" } : languages.ToLower().Split(',');
                var result = questions.Select(q => new
                {
                    q.Id,
                    q.PersonalityTypeId,
                    q.OrderNumber,
                    q.IsActive,
                    Text = new Dictionary<string, string>
                    {
                        { "en", requestedLanguages.Contains("en") ? q.TextEn : null },
                        { "ar", requestedLanguages.Contains("ar") ? q.TextAr : null },
                        { "ru", requestedLanguages.Contains("ru") ? q.TextRu : null },
                        { "fa", requestedLanguages.Contains("fa") ? q.TextFa : null }
                    }.Where(kvp => kvp.Value != null).ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                    Answer = testAttemptId.HasValue && q.Answers.Any() ? q.Answers.First().Response : null
                });

                return Ok(result);
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }
}