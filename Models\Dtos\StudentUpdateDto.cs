using System;
using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Models.Dtos
{
    public class StudentUpdateDto
    {
        public string? AddressCountry { get; set; }
        public string? AddressCity { get; set; }
        public string? AddressStreet { get; set; }
        // Personal Information
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Gender { get; set; }
        
        // Address and Nationality
        public string? CountryOfResidence { get; set; }
        public string? Citizenship { get; set; }
        public string? PassportNumber { get; set; }
        public string? TCKimlikNumber { get; set; }
        
        // Family Information
        public string? FatherName { get; set; }
        public string? Mothername { get; set; }
        
        // Education Information
        public string? CurrentStage { get; set; }
        public string? RegistrationType { get; set; }
        public string? SecondarySchoolCountry { get; set; }
        public string? SecondarySchoolName { get; set; }
        public string? SchoolOrUniversityName { get; set; }
        
        // Study Abroad Information
        public string? DestinationCountry { get; set; }
        public string? DegreeInterest { get; set; }
        public string? FieldOfStudyInterest { get; set; }
        public string? InterestedUniversities { get; set; }
        public string? Language { get; set; }
        
        // Consultant Information
        public int? ConsultantId { get; set; }
    }
}
