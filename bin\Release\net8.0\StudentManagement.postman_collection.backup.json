{
  "info": {
    "name": "StudentManagement API",
    "description": "Complete API collection for Student Management System with API Key Authentication",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "Consultants",
      "item": [
        {
          "name": "Get All Consultants",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Consultants?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Consultants"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Retrieve a list of all consultants with their basic information and student count."
          },
          "response": []
        },
        {
          "name": "Get Consultant by ID",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Consultants/1?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Consultants", "1"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Retrieve detailed information about a specific consultant including their assigned students."
          },
          "response": []
        },
        {
          "name": "Create New Consultant",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstName\": \"John\",\n  \"lastName\": \"Doe\",\n  \"phoneNumber\": \"+**********\",\n  \"imageURL\": \"https://example.com/consultants/johndoe.jpg\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/Consultants?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Consultants"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Create a new consultant with the provided details."
          },
          "response": []
        },
        {
          "name": "Update Consultant",
          "request": {
            "method": "PUT",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"id\": 1,\n  \"firstName\": \"John Updated\",\n  \"lastName\": \"Doe\",\n  \"phoneNumber\": \"+**********\",\n  \"imageURL\": \"https://example.com/consultants/johndoe-updated.jpg\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/Consultants/1?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Consultants", "1"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Update an existing consultant's information. Only the provided fields will be updated."
          },
          "response": []
        },
        {
          "name": "Get Consultant by Student ID",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Consultants/ByStudent/{{studentId}}?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Consultants", "ByStudent", "{{studentId}}"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Retrieve consultant information by student ID"
          },
          "response": []
        },
        {
          "name": "Delete Consultant",
          "request": {
            "method": "DELETE",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Consultants/1?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Consultants", "1"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Delete a consultant. Fails if the consultant has any assigned students."
          },
          "response": []
        }
      ]
    },
    {
      "name": "Auth",
      "item": [
        {
          "name": "Register",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstName\": \"{{firstName}}\",\n  \"lastName\": \"{{lastName}}\",\n  \"email\": \"{{userEmail}}\",\n  \"password\": \"{{userPassword}}\",\n  \"confirmPassword\": \"{{userPassword}}\",\n  \"phoneNumber\": \"{{phoneNumber}}\",\n  \"dateOfBirth\": \"{{dateOfBirth}}\",\n  \"gender\": \"{{gender}}\",\n  \"citizenship\": \"{{citizenship}}\",\n  \"countryOfResidence\": \"{{countryOfResidence}}\",\n  \"lang\": \"{{defaultLanguage}}\",\n  \"isholand\": true\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/auth/register",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "auth",
                "register"
              ]
            },
            "description": "Register a new user account"
          },
          "response": [
            {
              "name": "Successful Registration",
              "originalRequest": {
                "method": "POST",
                "header": [],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"firstName\": \"John\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"confirmPassword\": \"Password123!\",\n  \"phoneNumber\": \"+**********\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"Male\",\n  \"citizenship\": \"US\",\n  \"countryOfResidence\": \"US\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/register"
                }
              },
              "status": "Created",
              "code": 201,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"message\": \"User registered successfully\"\n}"
            }
          ]
        },
        {
          "name": "Login",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"email\": \"{{userEmail}}\",\n  \"password\": \"{{userPassword}}\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/auth/login",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "auth",
                "login"
              ]
            },
            "description": "Authenticate user and get JWT token"
          },
          "response": [
            {
              "name": "Successful Login",
              "originalRequest": {
                "method": "POST",
                "header": [],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/login"
                }
              },
              "status": "OK",
              "code": 200,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}"
            }
          ]
        },
        {
          "name": "Generate Password Reset Token",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"phoneNumber\": \"{{phoneNumber}}\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/auth/generate-reset-token",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "auth",
                "generate-reset-token"
              ]
            },
            "description": "Generate a password reset token for the given phone number"
          },
          "response": [
            {
              "name": "Success",
              "originalRequest": {
                "method": "POST",
                "header": [],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"phoneNumber\": \"+**********\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/generate-reset-token"
                }
              },
              "status": "OK",
              "code": 200,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"message\": \"Password reset token generated successfully\",\n  \"token\": \"CfDJ8...\"\n}"
            }
          ]
        },
        {
          "name": "Reset Password",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"phoneNumber\": \"{{phoneNumber}}\",\n  \"token\": \"{{resetToken}}\",\n  \"newPassword\": \"{{newPassword}}\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/auth/reset-password",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "auth",
                "reset-password"
              ]
            },
            "description": "Reset password using the token received from generate-reset-token"
          },
          "response": [
            {
              "name": "Success",
              "originalRequest": {
                "method": "POST",
                "header": [],
                "body": {
                  "mode": "raw",
                  "raw": "{\n  \"phoneNumber\": \"+**********\",\n  \"token\": \"CfDJ8...\",\n  \"newPassword\": \"NewSecurePassword123!\"\n}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/auth/reset-password"
                }
              },
              "status": "OK",
              "code": 200,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"message\": \"Password has been reset successfully.\"\n}"
            }
          ]
        }
      ]
    },
    {
      "name": "Students",
      "item": [
        {
          "name": "Upload Student Profile Image",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "body": {
              "mode": "form-data",
              "formdata": [
                {
                  "key": "file",
                  "type": "file",
                  "src": "/path/to/your/image.jpg"
                }
              ]
            },
            "url": {
              "raw": "{{baseUrl}}/api/Students/upload-image?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Students", "upload-image"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Upload a new profile image for the authenticated student. The image will be saved with the student's ID as the filename. Only JPG, JPEG, PNG, and GIF files are allowed."
          },
          "response": []
        },
        {
          "name": "Update Student",
          "request": {
            "method": "PUT",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstName\": \"John\",\n  \"lastName\": \"Doe\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"+905551234567\",\n  \"dateOfBirth\": \"1995-05-15T00:00:00\",\n  \"gender\": \"Male\",\n  \"countryOfResidence\": \"Turkey\",\n  \"citizenship\": \"Turkey\",\n  \"passportNumber\": \"P1234567\",\n  \"tCKimlikNumber\": \"**********1\",\n  \"fatherName\": \"Michael Doe\",\n  \"mothername\": \"Jane Doe\",\n  \"registrationType\": \"1\",\n  \"secondarySchoolCountry\": \"Turkey\",\n  \"secondarySchoolName\": \"Istanbul High School\",\n  \"schoolOrUniversityName\": \"Istanbul Technical University\",\n  \"currentStage\": \"Undergraduate\",\n  \"destinationCountry\": \"United States\",\n  \"degreeInterest\": \"Bachelor's\",\n  \"language\": \"English\",\n  \"fieldOfStudyInterest\": \"Computer Science\",\n  \"interestedUniversities\": \"MIT, Stanford, Harvard\",\n  \"consultantId\": 1\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            }
              ]
            },
            "url": {
              "raw": "{{baseUrl}}/api/Students?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "Students"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Update student information. Only the fields that are provided will be updated. The student ID is automatically retrieved from the authentication token."
          },
          "response": []
        },
        {
          "name": "Get All Students",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/students",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "students"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Get list of all students"
          },
          "response": [
            {
              "name": "Success",
              "originalRequest": {
                "method": "GET",
                "header": [],
                "url": {
                  "raw": "{{baseUrl}}/api/students"
                }
              },
              "status": "OK",
              "code": 200,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "[\n  {\n    \"id\": 1,\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\"\n  }\n]"
            }
          ]
        },
        {
          "name": "Get Student by ID",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Students/byid?lang={{defaultLanguage}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Students",
                "byid"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Get specific student details including consultant information"
          },
          "response": [
            {
              "name": "Success",
              "originalRequest": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{token}}",
                    "type": "text"
                  },
                  {
                    "key": "X-API-Key",
                    "value": "{{apiKey}}",
                    "type": "text"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/Students/byid?lang={{defaultLanguage}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "Students", "byid"],
                  "query": [{"key": "lang", "value": "{{defaultLanguage}}"}]
                }
              },
              "status": "OK",
              "code": 200,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"status\": true,\n  \"code\": 200,\n  \"message\": null,\n  \"language\": \"en\",\n  \"data\": {\n    \"id\": \"student123\",\n    \"firstName\": \"John\",\n    \"lastName\": \"Doe\",\n    \"email\": \"<EMAIL>\",\n    \"phoneNumber\": \"+905551234567\",\n    \"dateOfBirth\": \"1995-05-15T00:00:00\",\n    \"countryOfResidence\": \"Turkey\",\n    \"citizenship\": \"Turkey\",\n    \"passportNumber\": \"P1234567\",\n    \"fatherName\": \"Michael Doe\",\n    \"mothername\": \"Jane Doe\",\n    \"registrationType\": \"High School\",\n    \"secondarySchoolCountry\": \"Turkey\",\n    \"secondarySchoolName\": \"Istanbul High School\",\n    \"tCKimlikNumber\": \"**********1\",\n    \"gender\": \"Male\",\n    \"currentStage\": \"Application\",\n    \"schoolOrUniversityName\": \"Istanbul University\",\n    \"destinationCountry\": \"United States\",\n    \"degreeInterest\": \"Bachelor\",\n    \"language\": \"English\",\n    \"fieldOfStudyInterest\": \"Computer Science\",\n    \"interestedUniversities\": \"MIT, Stanford, Harvard\",\n    \"enrollmentDate\": \"2023-09-01T00:00:00\",\n    \"crmId\": \"CRM12345\",\n    \"consultant\": {\n      \"id\": \"consultant123\",\n      \"firstName\": \"Ahmet\",\n      \"lastName\": \"Yılmaz\",\n      \"phoneNumber\": \"+905551234568\",\n      \"imageURL\": \"https://example.com/consultants/ahmet-yilmaz.jpg\",\n      \"dateCreated\": \"2023-01-01T00:00:00\",\n      \"dateModified\": \"2023-06-01T00:00:00\"\n    }\n  }\n}"
            },
            {
              "name": "Not Found",
              "originalRequest": {
                "method": "GET",
                "header": [
                  {
                    "key": "Authorization",
                    "value": "Bearer {{token}}",
                    "type": "text"
                  },
                  {
                    "key": "X-API-Key",
                    "value": "{{apiKey}}",
                    "type": "text"
                  }
                ],
                "url": {
                  "raw": "{{baseUrl}}/api/Students/byid?lang={{defaultLanguage}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "Students", "byid"],
                  "query": [{"key": "lang", "value": "{{defaultLanguage}}"}]
                }
              },
              "status": "Not Found",
              "code": 404,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"status\": false,\n  \"code\": 404,\n  \"message\": \"Student not found\",\n  \"language\": \"en\",\n  \"data\": null\n}"
            },
            {
              "name": "Unauthorized",
              "originalRequest": {
                "method": "GET",
                "header": [],
                "url": {
                  "raw": "{{baseUrl}}/api/Students/byid?lang={{defaultLanguage}}",
                  "host": ["{{baseUrl}}"],
                  "path": ["api", "Students", "byid"],
                  "query": [{"key": "lang", "value": "{{defaultLanguage}}"}]
                }
              },
              "status": "Unauthorized",
              "code": 401,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{\n  \"status\": false,\n  \"code\": 401,\n  \"message\": \"User is not associated with a student account\",\n  \"language\": \"en\",\n  \"data\": null\n}"
            }
          ]
        }
      ]
    },
    {
      "name": "Questions",
      "item": [
        {
          "name": "Get Questions",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/questions",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "questions"
              ],
              "query": [
                {
                  "key": "languages",
                  "value": "{{supportedLanguages}}"
                },
                {
                  "key": "testAttemptId",
                  "value": "{{testAttemptId}}"
                },
                {
                  "key": "testType",
                  "value": "{{testType}}"
                }
              ]
            },
            "description": "Get questions with language support and test type"
          }
        },
        {
          "name": "Get Question by ID",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/questions/{{questionId}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "questions",
                "{{questionId}}"
              ],
              "query": [
                {
                  "key": "languages",
                  "value": "{{supportedLanguages}}"
                }
              ]
            },
            "description": "Get specific question details with language support"
          }
        }
      ]
    },
    {
      "name": "Test Attempts",
      "item": [
        {
          "name": "Get Test Results",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/TestAttempts/results/{{testAttemptId}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "TestAttempts", "results", "{{testAttemptId}}"]
            },
            "description": "Get detailed results for a completed test attempt including personality analysis and recommended majors."
          },
          "response": []
        },
        {
          "name": "Save Answer",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"questionId\": \"{{questionId}}\",\n  \"value\": \"{{answerValue}}\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}/answers/{{questionId}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "testattempts",
                "{{testAttemptId}}",
                "answers",
                "{{questionId}}"
              ]
            },
            "description": "Save an answer for a specific question in a test attempt"
          },
          "response": [
            {
              "name": "Successful Answer Save",
              "originalRequest": {
                "method": "POST",
                "header": [],
                "body": {
                  "mode": "raw",
                  "raw": "{  \"questionId\": 1,  \"value\": \"Sample answer text\"}"
                },
                "url": {
                  "raw": "{{baseUrl}}/api/testattempts/1/answers/1"
                }
              },
              "status": "OK",
              "code": 200,
              "header": [
                {
                  "key": "Content-Type",
                  "value": "application/json"
                }
              ],
              "body": "{  \"status\": true,  \"code\": 200,  \"message\": \"Answer saved successfully\",  \"data\": null}"
            }
          ]
        },
        {
          "name": "Get All Test Attempts",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/testattempts",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "testattempts"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Get all test attempts for current student"
          }
        },
        {
          "name": "Start Test Attempt",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"testType\": \"{{testType}}\",\n  \"language\": \"{{defaultLanguage}}\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/testattempts/start",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "testattempts",
                "start"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Start a new test attempt"
          }
        },
        {
          "name": "Update Answer (One by One)",
          "request": {
            "method": "PUT",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"value\": \"{{answerText}}\"\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}/answers/{{answerId}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "testattempts",
                "{{testAttemptId}}",
                "answers",
                "{{answerId}}"
              ]
            },
            "description": "Update answer for a specific question in test attempt (one by one)"
          }
        },
        {
          "name": "Submit All Answers",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"answers\": [\n    {\n      \"questionId\": 1,\n      \"value\": \"Answer for question 1\"\n    },\n    {\n      \"questionId\": 2,\n      \"value\": \"Answer for question 2\"\n    },\n    {\n      \"questionId\": 3,\n      \"value\": \"Answer for question 3\"\n    }\n  ]\n}"
            },
            "url": {
              "raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}/answers/batch",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "testattempts",
                "{{testAttemptId}}",
                "answers",
                "batch"
              ]
            },
            "description": "Submit all answers at once for a test attempt"
          }
        }
      ]
    },
    {
      "name": "File Upload",
      "item": [
        {
          "name": "Upload File",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "body": {
              "mode": "formdata",
              "formdata": [
                {
                  "key": "file",
                  "type": "file",
                  "src": []
                }
              ]
            },
            "url": {
              "raw": "{{baseUrl}}/api/fileupload/upload",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "fileupload",
                "upload"
              ]
            },
            "description": "Upload image file (jpg, jpeg, png, gif, bmp)"
          }
        }
      ]
    },
    {
      "name": "Application",
      "item": [
        {
          "name": "Get Term List",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Application/Termlist?lang={{defaultLanguage}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Application",
                "Termlist"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Get list of all available terms"
          }
        },
        {
          "name": "Get Degree List",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Application/Degreelist?Termid={{termId}}&lang={{defaultLanguage}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Application",
                "Degreelist"
              ],
              "query": [
                {
                  "key": "Termid",
                  "value": "{{termId}}"
                },
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Get list of degrees for a specific term"
          }
        },
        {
          "name": "Get Majors List",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/Application/Majorslist?Termid={{termId}}&Degree={{degree}}&lang={{defaultLanguage}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Application",
                "Majorslist"
              ],
              "query": [
                {
                  "key": "Termid",
                  "value": "{{termId}}"
                },
                {
                  "key": "Degree",
                  "value": "{{degree}}"
                },
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Get list of majors for a specific term and degree"
          }
        },
        {
          "name": "Submit Application",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"PreferredDegree\": \"Bachelor\",\n  \"CountryCode\": \"+1\",\n  \"RegistrationType\": \"New\",\n  \"Mothername\": \"Jane Smith\",\n  \"Passport\": \"*********\",\n  \"Fathername\": \"John Smith\",\n  \"School\": \"High School\",\n  \"Gender\": \"Male\",\n  \"DateOfBirth\": \"2000-01-01\",\n  \"Accountid\": \"{{accountId}}\",\n  \"Email\": \"{{userEmail}}\",\n  \"CheckedItems\": [\n    {\n      \"Id\": \"{{programId}}\",\n      \"Name\": \"Computer Science\",\n      \"UniversityId\": \"{{universityId}}\",\n      \"UniversityName\": \"{{universityName}}\",\n      \"IsSelected\": true,\n      \"Academic_Year__c\": \"{{academicYear}}\",\n      \"Program_Degree__c\": \"{{degree}}\",\n      \"Program__c\": \"{{programId}}\",\n      \"Semester__c\": \"{{semester}}\",\n      \"Term_Settings__c\": \"{{termId}}\",\n      \"University_Id__c\": \"{{universityId}}\",\n      \"University_Name__c\": \"{{universityName}}\",\n      \"Deposit_Price__c\": \"{{depositPrice}}\",\n      \"CurrencyType__c\": \"{{currencyType}}\"\n    }\n  ]\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/Application/SubmitApp?lang={{defaultLanguage}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Application",
                "SubmitApp"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Submit a new application with all required personal and academic information"
          }
        },
        {
          "name": "Update Degree",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json",
                "type": "text"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"PreferredDegree\": \"Master\",\n  \"RegistrationType\": \"Transfer\",\n  \"Accountid\": \"{{accountId}}\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/Application/updatedegree?lang={{defaultLanguage}}",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Application",
                "updatedegree"
              ],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Update a student's degree and registration type"
          }
        },
        {
          "name": "Upload File",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Authorization",
                "value": "Bearer {{token}}",
                "type": "text"
              },
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              }
            ],
            "body": {
              "mode": "formdata",
              "formdata": [
                {
                  "key": "file",
                  "type": "file",
                  "src": []
                },
                {
                  "key": "accountid",
                  "value": "{{accountId}}",
                  "type": "text"
                },
                {
                  "key": "filetype",
                  "value": "{{fileType}}",
                  "type": "text"
                }
              ]
            },
            "url": {
              "raw": "{{baseUrl}}/api/Application/uploadunitedfile",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "api",
                "Application",
                "uploadunitedfile"
              ]
            },
            "description": "Upload a file for an application (supports jpg, jpeg, png, gif, webp, pdf up to 20MB)"
          }
        }
      ]
    },
    {
      "name": "Events",
      "description": "Endpoints for managing events",
      "item": [
        {
          "name": "Get All Events",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/events?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "events"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Retrieve a list of all active events"
          },
          "response": []
        },
        {
          "name": "Get Event by ID",
          "request": {
            "method": "GET",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{authToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/events/1?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "events", "1"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Retrieve a specific event by its ID"
          },
          "response": []
        },
        {
          "name": "Create Event (Admin)",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{adminAuthToken}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"title\": \"Sample Event\",\n  \"description\": \"This is a sample event description\",\n  \"imageURL\": \"https://example.com/event.jpg\",\n  \"location\": \"Online\",\n  \"eventDate\": \"2025-07-15\",\n  \"startTime\": \"10:00\",\n  \"endTime\": \"12:00\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/events?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "events"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Create a new event (Admin only)"
          },
          "response": []
        },
        {
          "name": "Update Event (Admin)",
          "request": {
            "method": "PUT",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{adminAuthToken}}",
                "type": "text"
              },
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"id\": 1,\n  \"title\": \"Updated Event\",\n  \"description\": \"This is an updated event\",\n  \"imageURL\": \"https://example.com/updated-event.jpg\",\n  \"location\": \"Updated Location\",\n  \"eventDate\": \"2025-07-20\",\n  \"startTime\": \"14:00\",\n  \"endTime\": \"16:00\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/api/events/1?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "events", "1"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Update an existing event (Admin only)"
          },
          "response": []
        },
        {
          "name": "Delete Event (Admin)",
          "request": {
            "method": "DELETE",
            "header": [
              {
                "key": "X-API-Key",
                "value": "{{apiKey}}",
                "type": "text"
              },
              {
                "key": "Authorization",
                "value": "Bearer {{adminAuthToken}}",
                "type": "text"
              }
            ],
            "url": {
              "raw": "{{baseUrl}}/api/events/1?lang={{defaultLanguage}}",
              "host": ["{{baseUrl}}"],
              "path": ["api", "events", "1"],
              "query": [
                {
                  "key": "lang",
                  "value": "{{defaultLanguage}}"
                }
              ]
            },
            "description": "Delete an event (soft delete, Admin only)"
          },
          "response": []
        }
      ]
    }
  ],
  "variable": [
    {
      "key": "studentId",
      "value": "YOUR_STUDENT_ID_HERE",
      "type": "string",
      "description": "The ID of the student to get consultant info for"
    },
    {
      "key": "baseUrl",
      "value": "http://localhost:5000",
      "type": "string"
    },
    {
      "key": "token",
      "value": "",
      "type": "string"
    },
    {
      "key": "apiKey",
      "value": "",
      "type": "string"
    },
    {
      "key": "userEmail",
      "value": "<EMAIL>",
      "type": "string"
    },
    {
      "key": "userPassword",
      "value": "password123",
      "type": "string"
    },
    {
      "key": "defaultLanguage",
      "value": "en",
      "type": "string"
    },
    {
      "key": "supportedLanguages",
      "value": "en,ar,ru,fa",
      "type": "string"
    },
    {
      "key": "testType",
      "value": "full",
      "type": "string"
    },
    {
      "key": "studentId",
      "value": "1",
      "type": "string"
    },
    {
      "key": "questionId",
      "value": "1",
      "type": "string"
    },
    {
      "key": "testAttemptId",
      "value": "1",
      "type": "string"
    },
    {
      "key": "answerId",
      "value": "1",
      "type": "string"
    },
    {
      "key": "answerText",
      "value": "Answer text",
      "type": "string"
    },
    {
      "key": "firstName",
      "value": "John",
      "type": "string"
    },
    {
      "key": "lastName",
      "value": "Doe",
      "type": "string"
    },
    {
      "key": "phoneNumber",
      "value": "+**********",
      "type": "string"
    },
    {
      "key": "dateOfBirth",
      "value": "1990-01-01",
      "type": "string"
    },
    {
      "key": "gender",
      "value": "Male",
      "type": "string"
    },
    {
      "key": "citizenship",
      "value": "US",
      "type": "string"
    },
    {
      "key": "countryOfResidence",
      "value": "US",
      "type": "string"
    },
    {
      "key": "termId",
      "value": "",
      "type": "string"
    },
    {
      "key": "degree",
      "value": "",
      "type": "string"
    },
    {
      "key": "programId",
      "value": "",
      "type": "string"
    },
    {
      "key": "universityName",
      "value": "",
      "type": "string"
    },
    {
      "key": "programName",
      "value": "",
      "type": "string"
    },
    {
      "key": "academicYear",
      "value": "",
      "type": "string"
    },
    {
      "key": "semester",
      "value": "",
      "type": "string"
    },
    {
      "key": "universityId",
      "value": "",
      "type": "string"
    },
    {
      "key": "program",
      "value": "",
      "type": "string"
    },
    {
      "key": "depositPrice",
      "value": "",
      "type": "string"
    },
    {
      "key": "currencyType",
      "value": "",
      "type": "string"
    },
    {
      "key": "accountId",
      "value": "",
      "type": "string"
    },
    {
      "key": "fileType",
      "value": "",
      "type": "string"
    }
  ]
}