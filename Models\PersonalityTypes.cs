﻿using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Models
{
    public class PersonalityTypes
    {
        public int Id { get; set; }
        public string Code { get; set; }

        public string NameAr { get; set; }
        public string NameEn { get; set; }
        public string NameFa { get; set; }
        public string NameRu { get; set; }

        public string DescriptionAr { get; set; }
        public string DescriptionEn { get; set; }
        public string DescriptionFa { get; set; }
        public string DescriptionRu { get; set; }

        public string TraitsAr { get; set; }
        public string TraitsEn { get; set; }
        public string TraitsFa { get; set; }
        public string TraitsRu { get; set; }

        public string WorkEnvironmentAr { get; set; }
        public string WorkEnvironmentEn { get; set; }
        public string WorkEnvironmentFa { get; set; }
        public string WorkEnvironmentRu { get; set; }

        public string PreferredActivitiesAr { get; set; }
        public string PreferredActivitiesEn { get; set; }
        public string PreferredActivitiesFa { get; set; }
        public string PreferredActivitiesRu { get; set; }

        public string StrengthsAr { get; set; }
        public string StrengthsEn { get; set; }
        public string StrengthsFa { get; set; }
        public string StrengthsRu { get; set; }

        public string SkillsNeededAr { get; set; }
        public string SkillsNeededEn { get; set; }
        public string SkillsNeededFa { get; set; }
        public string SkillsNeededRu { get; set; }

        public string ActivitiesAr { get; set; }
        public string ActivitiesEn { get; set; }
        public string ActivitiesFa { get; set; }
        public string ActivitiesRu { get; set; }

        public string Icon { get; set; }
        public virtual ICollection<Question> Questions { get; set; } // Navigation property for related questions

        public PersonalityTypes()
        {
            Questions = new List<Question>();
        }

    }
}
