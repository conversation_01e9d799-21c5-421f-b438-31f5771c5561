using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;

namespace StudentManagementAPI.Services
{
    public class HollandCodeService : IHollandCodeService
    {
        private readonly ApplicationDbContext _context;

        public HollandCodeService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<HollandCodeResult> GetMajorsByHollandCodeAsync(string hollandCode, string language = "en")
        {
            if (string.IsNullOrWhiteSpace(hollandCode) || hollandCode.Length != 3)
            {
                throw new ArgumentException("Holland code must be exactly 3 characters long", nameof(hollandCode));
            }

            var result = await _context.UniversityMajors
                .FirstOrDefaultAsync(um => um.HollandCode.Equals(hollandCode, StringComparison.OrdinalIgnoreCase));

            if (result == null)
            {
                throw new KeyNotFoundException($"No majors found for Holland code: {hollandCode}");
            }

            var hollandResult = new HollandCodeResult
            {
                HollandCode = hollandCode.ToUpper(),
                CodeDescription = result.CodeDescription ?? string.Empty,
                SuggestedJobs = !string.IsNullOrEmpty(result.SuggestedJobs) 
                    ? result.SuggestedJobs.Split(',').Select(j => j.Trim()).ToList() 
                    : new List<string>(),
                UniversityMajor = result  // Include the full UniversityMajor object
            };

            // Get majors based on language preference
            string? majors = language.ToLower() switch
            {
                "ar" => result.MajorsArabic,
                "fa" => result.MajorsPersian,
                "ru" => result.MajorsRussian,
                _ => result.MajorsEnglish // Default to English
            };

            hollandResult.Majors = !string.IsNullOrEmpty(majors)
                ? majors.Split(',').Select(m => m.Trim()).ToList()
                : new List<string>();

            return hollandResult;
        }
    }
}
