using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.Models.Dtos;

namespace StudentManagementAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class EventsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public EventsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: api/Events
        [HttpGet]
        public async Task<ActionResult<IEnumerable<EventDto>>> GetEvents()
        {
            var events = await _context.Events
                .Where(e => e.IsActive)
                .OrderBy(e => e.EventDate)
                .Select(e => new EventDto
                {
                    Id = e.Id,
                    Title = e.Title,
                    Description = e.Description,
                    ImageURL = e.ImageURL,
                    Location = e.Location,
                    EventDate = e.EventDate,
                    StartTime = e.StartTime,
                    EndTime = e.EndTime,
                    IsActive = e.IsActive,
                    CreatedAt = e.CreatedAt
                })
                .ToListAsync();

            return Ok(events);
        }

        // GET: api/Events/5
        [HttpGet("{id}")]
        public async Task<ActionResult<EventDto>> GetEvent(int id)
        {
            var @event = await _context.Events
                .Where(e => e.Id == id && e.IsActive)
                .Select(e => new EventDto
                {
                    Id = e.Id,
                    Title = e.Title,
                    Description = e.Description,
                    ImageURL = e.ImageURL,
                    Location = e.Location,
                    EventDate = e.EventDate,
                    StartTime = e.StartTime,
                    EndTime = e.EndTime,
                    IsActive = e.IsActive,
                    CreatedAt = e.CreatedAt
                })
                .FirstOrDefaultAsync();

            if (@event == null)
            {
                return NotFound(new { message = "Event not found" });
            }

            return @event;
        }

        // POST: api/Events
        [Authorize(Roles = "Admin")]
        [HttpPost]
        public async Task<ActionResult<EventDto>> CreateEvent(CreateEventDto createEventDto)
        {
            var @event = new Event
            {
                Title = createEventDto.Title,
                Description = createEventDto.Description,
                ImageURL = createEventDto.ImageURL,
                Location = createEventDto.Location,
                EventDate = createEventDto.EventDate,
                StartTime = createEventDto.StartTime,
                EndTime = createEventDto.EndTime,
                IsActive = createEventDto.IsActive,
                CreatedAt = DateTime.Now
            };

            _context.Events.Add(@event);
            await _context.SaveChangesAsync();

            var eventDto = new EventDto
            {
                Id = @event.Id,
                Title = @event.Title,
                Description = @event.Description,
                ImageURL = @event.ImageURL,
                Location = @event.Location,
                EventDate = @event.EventDate,
                StartTime = @event.StartTime,
                EndTime = @event.EndTime,
                IsActive = @event.IsActive,
                CreatedAt = @event.CreatedAt
            };

            return CreatedAtAction(nameof(GetEvent), new { id = @event.Id }, eventDto);
        }

        // PUT: api/Events/5
        [Authorize(Roles = "Admin")]
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEvent(int id, UpdateEventDto updateEventDto)
        {
            var @event = await _context.Events.FindAsync(id);
            if (@event == null)
            {
                return NotFound(new { message = "Event not found" });
            }

            if (!string.IsNullOrEmpty(updateEventDto.Title))
                @event.Title = updateEventDto.Title;
                
            if (!string.IsNullOrEmpty(updateEventDto.Description))
                @event.Description = updateEventDto.Description;
                
            if (!string.IsNullOrEmpty(updateEventDto.ImageURL))
                @event.ImageURL = updateEventDto.ImageURL;
                
            if (!string.IsNullOrEmpty(updateEventDto.Location))
                @event.Location = updateEventDto.Location;
                
            if (updateEventDto.EventDate.HasValue)
                @event.EventDate = updateEventDto.EventDate.Value;
                
            if (updateEventDto.StartTime.HasValue)
                @event.StartTime = updateEventDto.StartTime.Value;
                
            if (updateEventDto.EndTime.HasValue)
                @event.EndTime = updateEventDto.EndTime.Value;
                
            if (updateEventDto.IsActive.HasValue)
                @event.IsActive = updateEventDto.IsActive.Value;

            @event.UpdatedAt = DateTime.Now;

            _context.Entry(@event).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EventExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // DELETE: api/Events/5
        [Authorize(Roles = "Admin")]
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteEvent(int id)
        {
            var @event = await _context.Events.FindAsync(id);
            if (@event == null)
            {
                return NotFound(new { message = "Event not found" });
            }

            @event.IsActive = false;
            @event.UpdatedAt = DateTime.Now;
            _context.Entry(@event).State = EntityState.Modified;

            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool EventExists(int id)
        {
            return _context.Events.Any(e => e.Id == id);
        }
    }
}
