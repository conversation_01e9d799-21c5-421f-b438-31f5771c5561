using Microsoft.AspNetCore.Identity;

namespace StudentManagementAPI.Models
{
    public class ApplicationUser : IdentityUser
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string? PassportNumber { get; set; }
        public string? FatherName { get; set; }
        public string? Mothername { get; set; }
        public string? RegistrationType { get; set; }
        public string? SecondarySchoolCountry { get; set; }
        public string? SecondarySchoolName { get; set; }
        public string? TCKimlikNumber { get; set; }
        public string? CrmId { get; set; }
        public string? CurrentStage { get; set; }
        public string? SchoolOrUniversityName { get; set; }
        public string? DestinationCountry { get; set; }
        public string? DegreeInterest { get; set; }
        public string? Language { get; set; }
        public string? FieldOfStudyInterest { get; set; }
        public string? InterestedUniversities { get; set; }
        public string Gender { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string? CountryOfResidence { get; set; }
        public string? Citizenship { get; set; }        // Navigation property to link student profile if needed
        public virtual Student Student { get; set; }
    }
}