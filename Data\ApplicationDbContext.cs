using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Models;

namespace StudentManagementAPI.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Student> Students { get; set; }
        public DbSet<Consultant> Consultants { get; set; }
        public DbSet<TestAttempt> TestAttempts { get; set; }
        public DbSet<Answer> Answers { get; set; }
        public DbSet<Question> Questions { get; set; }
        public DbSet<PersonalityTypes> PersonalityTypes { get; set; }
        public DbSet<MajorClassification> majorClassifications { get; set; }
        public DbSet<Event> Events { get; set; }
        public DbSet<Referral> Referrals { get; set; }
        public DbSet<UniversityMajor> UniversityMajors { get; set; }
        public DbSet<SmsVerification> SmsVerifications { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            
            // Configure unique constraints for Student
            builder.Entity<Student>()
                .HasIndex(s => s.Email)
                .IsUnique();

            builder.Entity<Student>()
                .HasIndex(s => s.PhoneNumber)
                .IsUnique();

            // Configure one-to-one relationship between ApplicationUser and Student
            builder.Entity<ApplicationUser>()
                .HasOne(u => u.Student)
                .WithOne(s => s.ApplicationUser)
                .HasForeignKey<Student>(s => s.ApplicationUserId)
                .OnDelete(DeleteBehavior.NoAction); // Change from Cascade to NoAction

            // Configure relationships for Question and PersonalityType
            builder.Entity<Question>()
                .HasOne(q => q.PersonalityTypes)
                .WithMany(p => p.Questions)
                .HasForeignKey(q => q.PersonalityTypeId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure one-to-many relationship between Consultant and Student
            builder.Entity<Student>()
                .HasOne<Consultant>(s => s.Consultant)
                .WithMany(c => c.Students)
                .HasForeignKey(s => s.ConsultantId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure relationships for Answer and TestAttempt
            builder.Entity<Answer>()
                .HasOne(a => a.TestAttempt)
                .WithMany(t => t.Answers)
                .HasForeignKey(a => a.TestAttemptId)
                .OnDelete(DeleteBehavior.Cascade);
 
            // Configure relationships for Answer and Question
            builder.Entity<Answer>()
                .HasOne(a => a.Question)
                .WithMany(q => q.Answers)
                .HasForeignKey(a => a.QuestionId)
                .OnDelete(DeleteBehavior.Restrict);

            // Ensure each answer is associated with both a question and a test attempt
            builder.Entity<Answer>()
                .HasIndex(a => new { a.QuestionId, a.TestAttemptId })
                .IsUnique(false);

            // Configure the relationship between Student and TestAttempt
            builder.Entity<TestAttempt>()
                .HasOne(t => t.Student)
                .WithMany(s => s.TestAttempts)
                .HasForeignKey(t => t.StudentId)
                .OnDelete(DeleteBehavior.NoAction); // Change from Cascade to NoAction
                
            // Configure relationships for Referral
            builder.Entity<Referral>()
                .HasOne(r => r.Referrer)
                .WithMany(s => s.ReferralsMade)
                .HasForeignKey(r => r.ReferrerId)
                .OnDelete(DeleteBehavior.NoAction);
                
            builder.Entity<Referral>()
                .HasOne(r => r.ReferredStudent)
                .WithMany(s => s.ReferralsReceived)
                .HasForeignKey(r => r.ReferredStudentId)
                .OnDelete(DeleteBehavior.NoAction);
        }
    }
}