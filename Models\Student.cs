using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace StudentManagementAPI.Models
{
    public class Student
    {
        public string Id { get; set; }
        public string RefCode { get; set; }
        public int Point{ get; set; }= 0;   
        public string? ImageURL { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string? CountryOfResidence { get; set; }
        public string? Citizenship { get; set; }
        public string? PassportNumber { get; set; }
        public string? FatherName { get; set; }
        public string? Mothername { get; set; }
        public string? RegistrationType { get; set; }
        public string? SecondarySchoolCountry { get; set; }
        public string? SecondarySchoolName { get; set; }
        public string? TCKimlikNumber    { get; set; }
        public string Gender { get; set; }
        public string? CurrentStage { get; set; }
        public string? SchoolOrUniversityName { get; set; }
        public string? DestinationCountry { get; set; }
        public string? DegreeInterest { get; set; }
        public string? Language { get; set; }
        public string? FieldOfStudyInterest { get; set; }
        public string? InterestedUniversities { get; set; }

        public DateTime EnrollmentDate { get; set; } = DateTime.Now;
        public string CrmId { get; set; }
        public int arrange { get; set; }
        public DateTime datecreated { get; set; }=DateTime.Now;
        public DateTime datemodified { get; set; } = DateTime.Now;
        public string? IP { get; set; }
        public string? modifiedIP { get; set; }
        public string lang { get; set; }
        public string? user { get; set; }
        public string year { get; set; }
        public string guid { get; set; }
        public string? ApplicationUserId { get; set; }
        public string? AddressCountry { get; set; }
        public string? AddressCity { get; set; }
        public string? AddressStreet { get; set; }
        public virtual ApplicationUser? ApplicationUser { get; set; }

        // Consultant relationship
        public int? ConsultantId { get; set; }
        public virtual Consultant? Consultant { get; set; }

        // Navigation properties
        public virtual ICollection<TestAttempt> TestAttempts { get; set; }
        
        // Referral relationships
        public virtual ICollection<Referral> ReferralsMade { get; set; } // Students this student has referred
        public virtual ICollection<Referral> ReferralsReceived { get; set; } // Referrals this student has received
    }
}