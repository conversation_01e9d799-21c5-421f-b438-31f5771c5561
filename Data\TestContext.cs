using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Models;

namespace StudentManagementAPI.Data
{
    public class TestContext : DbContext
    {
        public TestContext(DbContextOptions<TestContext> options)
            : base(options)
        {
        }
        public virtual DbSet<setting> Settings { get; set; }
        // All DbSets have been moved to ApplicationDbContext

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            
            // This context is now empty as all entities have been moved to ApplicationDbContext
        }
    }
}