internal class RegistrationRateLimitMiddleware
{
    private readonly RequestDelegate _next;

    public RegistrationRateLimitMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip rate limiting for authentication endpoints
        if (context.Request.Path.StartsWithSegments("/callback") || 
            context.Request.Path.StartsWithSegments("/signin-"))
        {
            await _next(context);
            return;
        }

        // Only apply rate limiting to the registration endpoint
        if (IsRegistrationEndpoint(context))
        {
            // Get client identifier (IP address or API key if available)
            string clientId = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            
        }

        // Continue processing the request
        await _next(context);
    }

    private bool IsRegistrationEndpoint(HttpContext context)
    {
        return context.Request.Path.StartsWithSegments("/register");
    }
}
