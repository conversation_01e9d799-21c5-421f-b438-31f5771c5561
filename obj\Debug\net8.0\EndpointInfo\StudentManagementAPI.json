{"openapi": "3.0.1", "info": {"title": "Student Management API", "version": "v1"}, "paths": {"/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/FileUpload/upload": {"post": {"tags": ["FileUpload"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Questions": {"get": {"tags": ["Questions"], "parameters": [{"name": "languages", "in": "query", "schema": {"type": "string"}}, {"name": "testAttemptId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "testType", "in": "query", "schema": {"type": "string", "default": "full"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Questions/{id}": {"get": {"tags": ["Questions"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "languages", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Questions/active": {"get": {"tags": ["Questions"], "parameters": [{"name": "languages", "in": "query", "schema": {"type": "string"}}, {"name": "testAttemptId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "testType", "in": "query", "schema": {"type": "string", "default": "full"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Students": {"get": {"tags": ["Students"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Student"}}}}}}}}, "/api/Students/{id}": {"get": {"tags": ["Students"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/Student"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Student"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Student"}}}}}}, "put": {"tags": ["Students"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Student"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Student"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Student"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Students"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts": {"get": {"tags": ["TestAttempts"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TestAttempt"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TestAttempt"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TestAttempt"}}}}}}}}, "/api/TestAttempts/{id}": {"get": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "testType", "in": "query", "schema": {"type": "string", "default": "full"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts/{id}/pause": {"put": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts/{id}/resume": {"put": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts/{id}/name": {"put": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts/{id}/complete": {"put": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts/{testAttemptId}/answers/{questionId}": {"put": {"tags": ["TestAttempts"], "parameters": [{"name": "testAttemptId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "questionId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAnswerRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAnswerRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAnswerRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TestAttempts/start": {"post": {"tags": ["TestAttempts"], "parameters": [{"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartTestAttemptRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StartTestAttemptRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StartTestAttemptRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/TestAttempts/{id}/answer": {"post": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveAnswerRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SaveAnswerRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SaveAnswerRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/TestAttempts/{id}/questions": {"get": {"tags": ["TestAttempts"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "lang", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}}, "components": {"schemas": {"Answer": {"required": ["response"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "questionId": {"type": "integer", "format": "int32"}, "question": {"$ref": "#/components/schemas/Question"}, "testAttemptId": {"type": "integer", "format": "int32"}, "testAttempt": {"$ref": "#/components/schemas/TestAttempt"}, "response": {"minLength": 1, "type": "string"}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApiResponse": {"type": "object", "properties": {"status": {"type": "boolean"}, "code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApplicationUser": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "normalizedUserName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "normalizedEmail": {"type": "string", "nullable": true}, "emailConfirmed": {"type": "boolean"}, "passwordHash": {"type": "string", "nullable": true}, "securityStamp": {"type": "string", "nullable": true}, "concurrencyStamp": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "phoneNumberConfirmed": {"type": "boolean"}, "twoFactorEnabled": {"type": "boolean"}, "lockoutEnd": {"type": "string", "format": "date-time", "nullable": true}, "lockoutEnabled": {"type": "boolean"}, "accessFailedCount": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "crmId": {"type": "string", "nullable": true}, "student": {"$ref": "#/components/schemas/Student"}}, "additionalProperties": false}, "LoginRequest": {"required": ["emailOrPhone", "password"], "type": "object", "properties": {"emailOrPhone": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "PersonalityType": {"required": ["code", "descriptionAr", "descriptionEn", "descriptionFa", "descriptionRu", "nameAr", "nameEn", "namefa", "nameRu"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "code": {"minLength": 1, "type": "string"}, "nameEn": {"maxLength": 100, "minLength": 1, "type": "string"}, "nameAr": {"maxLength": 100, "minLength": 1, "type": "string"}, "nameRu": {"maxLength": 100, "minLength": 1, "type": "string"}, "namefa": {"maxLength": 100, "minLength": 1, "type": "string"}, "descriptionEn": {"minLength": 1, "type": "string"}, "descriptionAr": {"minLength": 1, "type": "string"}, "descriptionRu": {"minLength": 1, "type": "string"}, "descriptionFa": {"minLength": 1, "type": "string"}, "questions": {"type": "array", "items": {"$ref": "#/components/schemas/Question"}, "nullable": true}}, "additionalProperties": false}, "Question": {"required": ["textAr", "textEn", "textFa", "textRu"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "textEn": {"minLength": 1, "type": "string"}, "textAr": {"minLength": 1, "type": "string"}, "textRu": {"minLength": 1, "type": "string"}, "textFa": {"minLength": 1, "type": "string"}, "personalityTypeId": {"type": "integer", "format": "int32"}, "personalityType": {"$ref": "#/components/schemas/PersonalityType"}, "orderNumber": {"type": "integer", "format": "int32"}, "isActive": {"type": "boolean"}, "answers": {"type": "array", "items": {"$ref": "#/components/schemas/Answer"}, "nullable": true}}, "additionalProperties": false}, "RegisterRequest": {"required": ["citizenship", "confirmPassword", "countryOfResidence", "email", "firstName", "gender", "isholand", "lang", "lastName", "password", "phoneNumber"], "type": "object", "properties": {"firstName": {"maxLength": 50, "minLength": 0, "type": "string"}, "lastName": {"maxLength": 50, "minLength": 0, "type": "string"}, "email": {"maxLength": 100, "minLength": 0, "type": "string", "format": "email"}, "phoneNumber": {"maxLength": 20, "minLength": 0, "type": "string"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "address": {"type": "string", "nullable": true}, "gender": {"maxLength": 20, "minLength": 0, "type": "string"}, "citizenship": {"maxLength": 100, "minLength": 0, "type": "string"}, "countryOfResidence": {"maxLength": 100, "minLength": 0, "type": "string"}, "lang": {"minLength": 1, "type": "string"}, "isholand": {"type": "boolean"}}, "additionalProperties": false}, "SaveAnswerRequest": {"required": ["questionId", "value"], "type": "object", "properties": {"questionId": {"type": "integer", "format": "int32"}, "value": {"minLength": 1, "type": "string"}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StartTestAttemptRequest": {"type": "object", "properties": {"testName": {"type": "string", "nullable": true}, "testType": {"type": "string", "nullable": true}, "language": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Student": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time"}, "countryOfResidence": {"type": "string", "nullable": true}, "citizenship": {"type": "string", "nullable": true}, "gender": {"type": "string", "nullable": true}, "enrollmentDate": {"type": "string", "format": "date-time"}, "crmId": {"type": "string", "nullable": true}, "arrange": {"type": "integer", "format": "int32"}, "datecreated": {"type": "string", "format": "date-time"}, "datemodified": {"type": "string", "format": "date-time"}, "ip": {"type": "string", "nullable": true}, "modifiedIP": {"type": "string", "nullable": true}, "lang": {"type": "string", "nullable": true}, "user": {"type": "string", "nullable": true}, "year": {"type": "string", "nullable": true}, "guid": {"type": "string", "nullable": true}, "applicationUserId": {"type": "string", "nullable": true}, "applicationUser": {"$ref": "#/components/schemas/ApplicationUser"}, "testAttempts": {"type": "array", "items": {"$ref": "#/components/schemas/TestAttempt"}, "nullable": true}}, "additionalProperties": false}, "TestAttempt": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "isCompleted": {"type": "boolean"}, "status": {"type": "string", "nullable": true}, "result": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "testType": {"type": "string", "nullable": true}, "totalQuestions": {"type": "integer", "format": "int32"}, "completionPercentage": {"type": "number", "format": "double"}, "lastResumeTime": {"type": "string", "format": "date-time", "nullable": true}, "answers": {"type": "array", "items": {"$ref": "#/components/schemas/Answer"}, "nullable": true}, "studentId": {"type": "string", "nullable": true}, "student": {"$ref": "#/components/schemas/Student"}}, "additionalProperties": false}, "UpdateAnswerRequest": {"required": ["value"], "type": "object", "properties": {"value": {"minLength": 1, "type": "string"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Enter 'Bearer' [space] and then your token in the text input below.", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}