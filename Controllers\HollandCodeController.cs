using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StudentManagementAPI.Services;
using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Requires authentication
    public class HollandCodeController : ControllerBase
    {
        private readonly IHollandCodeService _hollandCodeService;
        private readonly ILogger<HollandCodeController> _logger;

        public HollandCodeController(
            IHollandCodeService hollandCodeService,
            ILogger<HollandCodeController> logger)
        {
            _hollandCodeService = hollandCodeService;
            _logger = logger;
        }

        /// <summary>
        /// Get majors and suggested jobs by Holland code
        /// </summary>
        /// <param name="code">3-letter Holland code (e.g., "RIA", "SEC")</param>
        /// <param name="language">Language code (en, ar, fa, ru). Default is 'en'</param>
        /// <returns>Holland code result with majors and suggested jobs</returns>
        [HttpGet("{code}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<HollandCodeResult>> GetHollandCodeInfo(
            [Required][StringLength(3, MinimumLength = 3)] string code,
            [FromQuery] string language = "en")
        {
            try
            {
                var result = await _hollandCodeService.GetMajorsByHollandCodeAsync(code, language);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid Holland code: {Code}", code);
                return BadRequest(new { message = ex.Message });
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning(ex, "Holland code not found: {Code}", code);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving Holland code information for code: {Code}", code);
                return StatusCode(500, new { message = "An error occurred while processing your request." });
            }
        }
    }
}
