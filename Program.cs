using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.Services;
using StudentManagementAPI.Security;
using StudentManagementAPI.service;
using System.Text;
using StudentManagementAPI.Configuration;


var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.Preserve;
    });

// Configure CORS for multiple domains
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowMultipleDomains", policy =>
    {
        policy.WithOrigins(
             "http://localhost:3000",
               "http://localhost:3001",
             "https://localhost:3000",
             "https://unitededucation.com",
             "https://accounts.google.com",  // For Google OAuth
             "https://www.googleapis.com"    // For Google API calls
         )
         .AllowAnyMethod()
         .AllowAnyHeader()
         .AllowCredentials()
         .WithExposedHeaders(
             "Content-Disposition",
             "Authorization",                // For JWT tokens
             "X-Requested-With",             // Common header for AJAX requests
             "X-XSRF-TOKEN"                  // CSRF protection
         )
         .SetIsOriginAllowedToAllowWildcardSubdomains()
         .SetPreflightMaxAge(TimeSpan.FromMinutes(10));  // Cache preflight requests

    });
});

// Configure database connections
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"),
                    sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));

// Configure TestContext for legacy database
builder.Services.AddDbContext<TestContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("TestContext"),
                    sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));
builder.Services.AddDbContext<sit>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("sit"),
                    sqlServerOptionsAction: sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure();
                    }));
builder.Services.AddScoped<getuni2023>();
builder.Services.AddScoped<RsaHelper>();
// Configure Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 6;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure JWT Authentication
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultSignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.SaveToken = true;
    options.RequireHttpsMetadata = false;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"])),
        ClockSkew = TimeSpan.Zero
    };
})
.AddCookie();

// Configure social authentication
builder.Services.AddAuthentication()
.AddGoogle(googleOptions =>
{
    googleOptions.ClientId = builder.Configuration["Authentication:Google:ClientId"];
    googleOptions.ClientSecret = builder.Configuration["Authentication:Google:ClientSecret"];
    googleOptions.CallbackPath = "/api/Auth/external-login-callback";
    googleOptions.SaveTokens = true;
})
    .AddFacebook(facebookOptions =>
    {
        facebookOptions.AppId = builder.Configuration["Authentication:Facebook:AppId"];
        facebookOptions.AppSecret = builder.Configuration["Authentication:Facebook:AppSecret"];
        facebookOptions.CallbackPath = "/api/Auth/external-login-callback";
        facebookOptions.SaveTokens = true;
    })
    .AddMicrosoftAccount(microsoftOptions =>
    {
        microsoftOptions.ClientId = builder.Configuration["Authentication:Microsoft:ClientId"];
        microsoftOptions.ClientSecret = builder.Configuration["Authentication:Microsoft:ClientSecret"];
        microsoftOptions.CallbackPath = "/api/Auth/external-login-callback";
        microsoftOptions.SaveTokens = true;
    });

// Apple authentication requires additional NuGet packages
// Uncomment and configure if needed
/*
var appleClientId = builder.Configuration["Authentication:Apple:ClientId"];
var appleKeyId = builder.Configuration["Authentication:Apple:KeyId"];
var appleTeamId = builder.Configuration["Authentication:Apple:TeamId"];
var applePrivateKey = builder.Configuration["Authentication:Apple:PrivateKey"];

if (!string.IsNullOrEmpty(appleClientId) && !string.IsNullOrEmpty(appleKeyId) && 
    !string.IsNullOrEmpty(appleTeamId) && !string.IsNullOrEmpty(applePrivateKey))
{
    authBuilder.AddApple(appleOptions =>
    {
        appleOptions.ClientId = appleClientId;
        appleOptions.KeyId = appleKeyId;
        appleOptions.TeamId = appleTeamId;
        appleOptions.PrivateKey = applePrivateKey;
        appleOptions.PrivateKeyBytes = _ => Task.FromResult(Encoding.UTF8.GetBytes(applePrivateKey));
        appleOptions.SaveTokens = true;
    });
}
*/

// Add API security services (rate limiting)
builder.Services.AddApiSecurity();

// Add HttpClientFactory for creating HttpClient instances
builder.Services.AddHttpClient();

// Register application services
builder.Services.AddScoped<ISalesforceService, SalesforceService>();
builder.Services.AddScoped<ILanguageService, LanguageService>();
builder.Services.AddScoped<IHollandCodeService, HollandCodeService>();
builder.Services.AddHttpContextAccessor();

builder.Services.AddDbContext<UniversityProgramsDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("UniversityProgramsDb")));
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Student Management API", Version = "v1" });
    
    // Configure Swagger to use JWT Authentication
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Enter 'Bearer' [space] and then your token in the text input below.",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// Register Salesforce configuration
builder.Services.Configure<SalesforceSettings>(builder.Configuration.GetSection("Salesforce"));

// Register services
builder.Services.AddScoped<ILanguageService, LanguageService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Use CORS middleware - Must be before other middleware
app.UseCors("AllowMultipleDomains");

app.UseHttpsRedirection();

// Add API security middleware (rate limiting)
app.UseApiSecurity();
app.UseStaticFiles();

// Add authentication and authorization middleware
app.UseAuthentication();
app.UseAuthorization();
app.UseMiddleware<ApiKeyMiddleware>();
app.MapControllers();

// Create roles if they don't exist
using (var scope = app.Services.CreateScope())
{
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();
    var roles = new[] { "Admin", "Student", "Instructor" };
    
    foreach (var role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
        {
            await roleManager.CreateAsync(new IdentityRole(role));
        }
    }
}

app.Run();
