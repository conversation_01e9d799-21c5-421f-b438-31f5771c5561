# Design Document

## Overview

The Google OAuth authentication error occurs due to a mismatch between the configured callback path and the actual endpoint handling the OAuth response. The current configuration has inconsistencies that prevent proper callback processing, leading to authentication failures after successful Google login.

## Root Cause Analysis

Based on the code review, the main issues are:

1. **Callback Path Mismatch**: The Google OAuth configuration in `Program.cs` sets `CallbackPath = "/signin-google"`, but the actual callback handler is at `/api/Auth/external-login-callback`
2. **Route Confusion**: The `/signin-google` endpoint initiates OAuth, but Google redirects back to the same path expecting it to handle the callback
3. **Missing Error Handling**: Limited error handling for OAuth callback failures
4. **Configuration Inconsistency**: The appsettings.json specifies a different callback path than what's configured in the OAuth provider setup

## Architecture

The solution involves fixing the OAuth flow to follow this corrected sequence:

```
User → /api/Auth/signin-google → Google OAuth → /api/Auth/external-login-callback → JWT Token + Redirect
```

## Components and Interfaces

### 1. OAuth Configuration Fix

**Component**: Google OAuth Provider Configuration in `Program.cs`
- Fix the callback path to match the actual handler endpoint
- Ensure proper redirect URI configuration
- Add proper error handling configuration

### 2. Controller Route Alignment

**Component**: `AuthController.cs`
- Ensure the callback handler route matches the OAuth configuration
- Implement proper error handling for callback failures
- Add comprehensive logging for troubleshooting

### 3. Google Console Configuration

**Component**: External Google OAuth App Configuration
- Verify the authorized redirect URIs in Google Console match the application endpoints
- Ensure the OAuth consent screen is properly configured

## Data Models

### OAuth Callback Response Model
```csharp
public class OAuthCallbackResult
{
    public bool Success { get; set; }
    public string Token { get; set; }
    public string ErrorMessage { get; set; }
    public string RedirectUrl { get; set; }
}
```

### OAuth Error Model
```csharp
public class OAuthError
{
    public string Error { get; set; }
    public string ErrorDescription { get; set; }
    public string State { get; set; }
    public DateTime Timestamp { get; set; }
}
```

## Error Handling

### 1. Configuration Validation
- Validate OAuth settings on application startup
- Log missing or invalid configuration parameters
- Provide clear error messages for configuration issues

### 2. Callback Error Handling
- Handle Google OAuth error responses (access_denied, invalid_request, etc.)
- Implement proper logging for failed authentication attempts
- Provide user-friendly error messages for different failure scenarios

### 3. State Validation
- Validate the OAuth state parameter to prevent CSRF attacks
- Handle state mismatch errors appropriately
- Log suspicious authentication attempts

## Testing Strategy

### 1. Unit Tests
- Test OAuth configuration validation
- Test callback handler with various success/failure scenarios
- Test JWT token generation for OAuth users
- Test error handling paths

### 2. Integration Tests
- Test complete OAuth flow from initiation to callback
- Test with actual Google OAuth endpoints (in test environment)
- Test error scenarios with malformed callbacks
- Test state parameter validation

### 3. Manual Testing
- Test OAuth flow in browser with actual Google account
- Test error scenarios (denied access, invalid state, etc.)
- Verify proper redirect behavior after authentication
- Test with different user account states (new user, existing user)

## Implementation Steps

### Phase 1: Configuration Fix
1. Update Google OAuth configuration in `Program.cs`
2. Align callback paths between configuration and handlers
3. Update appsettings.json with correct callback path
4. Verify Google Console redirect URI configuration

### Phase 2: Enhanced Error Handling
1. Implement comprehensive error handling in callback method
2. Add detailed logging for OAuth flow steps
3. Create user-friendly error responses
4. Add state parameter validation

### Phase 3: Testing and Validation
1. Implement unit tests for OAuth components
2. Create integration tests for complete flow
3. Perform manual testing with various scenarios
4. Validate security aspects of the implementation

## Security Considerations

1. **State Parameter**: Ensure proper state parameter generation and validation to prevent CSRF attacks
2. **Token Security**: Secure JWT token generation and transmission
3. **Error Information**: Avoid exposing sensitive information in error messages
4. **Logging**: Log security-relevant events without exposing sensitive data
5. **HTTPS**: Ensure all OAuth endpoints use HTTPS in production