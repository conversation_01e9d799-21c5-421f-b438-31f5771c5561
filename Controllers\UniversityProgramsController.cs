﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Controllers;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using static StudentManagementAPI.Controllers.ApplicationController;

[ApiController]
[Route("api/[controller]")]
public class UniversityProgramsController : ControllerBase
{

    private readonly UniversityProgramsDbContext _context;
    private readonly sit _contextuni;
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILanguageService _languageService;
    private readonly RsaHelper _uniService1;
    private readonly ILogger<ApplicationController> _logger;
    private readonly IOptions<SalesforceSettings> _salesforceSettings;


    public UniversityProgramsController(
        UniversityProgramsDbContext context,
          sit contextuni,
        UserManager<ApplicationUser> userManager,
        ILanguageService languageService,
        getuni2023 uniService,
        RsaHelper uniService1,
        ILogger<ApplicationController> logger,
        IOptions<SalesforceSettings> salesforceSettings)
    {
        _contextuni = contextuni;
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _languageService = languageService ?? throw new ArgumentNullException(nameof(languageService));
        _uniService = uniService;
        _uniService1 = uniService1;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _salesforceSettings = salesforceSettings ?? throw new ArgumentNullException(nameof(salesforceSettings));
    }
    private readonly getuni2023 _uniService;
    string ConvertStringNull(string? input)
    {
        return (input == null || input.Trim().ToLower() == "null") ? "null" : input;
    }
    [HttpPost("InsertUniversityProgram")]
    public async Task<IActionResult> InsertUniversityProgram([FromBody] UniversityProgram program)
    {


        if (program == null || string.IsNullOrWhiteSpace(program.Program__c))
        {
            return BadRequest("Invalid program data.");
        }

        // 🗑 Remove existing program with same Program__c
        var existingProgram = _context.UniversityProgram.FirstOrDefault(p => p.Id == program.Id);

        if (existingProgram != null)
        {
            _context.UniversityProgram.Remove(existingProgram);
            await _context.SaveChangesAsync(); // Save delete first to avoid primary key conflicts
        }
        var matchingUniversity = await _context.University
                .FirstOrDefaultAsync(u => u.SalceId == program.University_Id__c);

        string logo = matchingUniversity != null ?
            "https://unitededucation.com/uniteduni/" + matchingUniversity.LinkLogo : "NULL";
        string universityNameAr = _contextuni.unidaynas.Where(a => a.newsid.Contains(program.University_Id__c)).Select(a => a.uniname).FirstOrDefault();
        if (program.University_Id__c == "001P200000cbN7uIAE")
        {
            universityNameAr = "جامعة برلين للأعمال والابتكار";
        }
        else if (program.University_Id__c == "001P200000cbN7vIAE")
        {
            universityNameAr = "جامعة كونستركتور";
        }
        else if (program.University_Id__c == "001P200000cbN7yIAE")
        {
            universityNameAr = "جامعة جيسما للعلوم التطبيقية";
        }
        else if (program.University_Id__c == "001P200000cbN81IAE")
        {
            universityNameAr = "جامعة برلين للعلوم التطبيقية SRH";
        }
        else if (program.University_Id__c == "0018d00000lzFFWAA2")
        {
            universityNameAr = "Acibadem University";
        }
        else if (program.University_Id__c == "001P200000GMojbIAD")
        {
            universityNameAr = "جامعة قبرص الدولية";
        }
        else if (program.University_Id__c == "001P200000GN1tSIAT")
        {
            universityNameAr = "جامعة فاينل الدولية";
        }
        program.OwnerId = Guid.NewGuid().ToString();
        // ➕ Insert the new program
        program.University_Logo__c = logo;
        program.University_NameAr = universityNameAr;
        program.University_NameRu = program.University_Name__c;
        program.University_NameFa = program.University_Name__c;
        program.ProgramFa = program.Program_Name__c;
        program.ProgramRu = program.Program_Name__c;
        program.Year__c = DateTime.Now.Year.ToString();
        program.Alternative_Program_Name__c = ConvertStringNull(program.Alternative_Program_Name__c);
        program.Campus__c = ConvertStringNull(program.Campus__c);
        program.CurrencyType__c = ConvertStringNull(program.CurrencyType__c);
        program.Language__c = ConvertStringNull(program.Language__c);
        program.Program__c = ConvertStringNull(program.Program__c);
        program.Program_Degree__c = ConvertStringNull(program.Program_Degree__c);
        program.Program_Name__c = ConvertStringNull(program.Program_Name__c);
        program.Semester__c = ConvertStringNull(program.Semester__c);
        program.University_Id__c = ConvertStringNull(program.University_Id__c);
        program.University_Name__c = ConvertStringNull(program.University_Name__c);
        program.University_Account_Billing_Country__c = ConvertStringNull(program.University_Account_Billing_Country__c);
        program.ProgramFa = ConvertStringNull(program.ProgramFa);
        program.ProgramRu = ConvertStringNull(program.ProgramRu);
        program.University_NameAr = ConvertStringNull(program.University_NameAr);
        program.University_NameRu = ConvertStringNull(program.University_NameRu);
        program.University_NameFa = ConvertStringNull(program.University_NameFa);
        program.University_Logo__c = ConvertStringNull(program.University_Logo__c);
        program.Academic_Year__c = ConvertStringNull(program.Academic_Year__c);
        program.CreatedById = ConvertStringNull(program.CreatedById);
        program.Discipline__c = ConvertStringNull(program.Discipline__c);
        program.Duration__c = ConvertStringNull(program.Duration__c);
        program.Faculty_Institute__c = ConvertStringNull(program.Faculty_Institute__c);
        program.LastModifiedById = ConvertStringNull(program.LastModifiedById);
        program.Old_Id__c = ConvertStringNull(program.Old_Id__c);
        program.OwnerId = ConvertStringNull(program.OwnerId);
        program.Name = ConvertStringNull(program.Name);
        program.Term_Settings__c = ConvertStringNull(program.Term_Settings__c);
        program.University_Billing_Country__c = ConvertStringNull(program.University_Billing_Country__c);
        program.University_City__c = ConvertStringNull(program.University_City__c);
        program.with_Thesis_non_Thesis__c = ConvertStringNull(program.with_Thesis_non_Thesis__c);
        program.Year__c = ConvertStringNull(program.Year__c);

        _context.UniversityProgram.Add(program);
        await _context.SaveChangesAsync();

        return Ok(new
        {
            message = "Program inserted successfully.",
            inserted = program
        });
    }
    [HttpGet("Termlist")]
    public async Task<ActionResult<ApiResponse>> Termlist()
    {

        string query = "SELECT Id,Term_Name_Formula__c,Semester__c,Academic_Year__c FROM TermSettings__c where Active__c=True and Record_Type_Developer_Name__c='Term_Settings'";

        //Query
        var result = await _uniService.Query(query);

        JObject json = JObject.Parse(result);
        Rootlist1 json1 = JsonConvert.DeserializeObject<Rootlist1>(result);
        await _context.Database.ExecuteSqlRawAsync("DELETE FROM TermSetting");

        foreach (var record in json1.records)
        {
            bool exists = await _context.TermSetting.AnyAsync(x => x.Id == record.Id);
            if (!exists)
            {
                var termSetting = new TermSetting
                {
                    Id = record.Id,
                    Term_Name_Formula = record.Term_Name_Formula__c,
                    Semester = record.Semester__c,
                    Academic_Year = record.Academic_Year__c
                };

                _context.TermSetting.Add(termSetting);
            }
        }

        // Save all changes at once
        await _context.SaveChangesAsync();


        return Ok(new ApiResponse
        {
            Status = true,
            Code = 200,
            Message = "",
            Data = json1,
            Language = "en"
        });
    }
    public class Rootlistd
    {
        public int totalSize { get; set; }
        public bool done { get; set; }
        public string nextRecordsUrl { get; set; }
        public List<Recordd> records { get; set; }
    }

    public class Recordd
    {
        public string Id { get; set; }
        public string Term_Name_Formula__c { get; set; }
        public string Name { get; set; }
        public string Semester__c { get; set; }
        public string Academic_Year__c { get; set; }
        public string Term_Logo__c { get; set; }
        public string Degree__c { get; set; }

        public string Term_Settings__c { get; set; }    
    }

    [HttpGet("Degreelist")]
    public async Task<ActionResult<ApiResponse>> Degreelist()
    {
    
       
        var exists =  _context.TermSetting.ToList();
        foreach (var item in exists)
        {
            //prepare the query
            string query1 = "SELECT Id,Term_Name_Formula__c,Name,Semester__c,Academic_Year__c,Term_Logo__c,Degree__c,Term_Settings__c FROM TermSettings__c where Active__c=True and Term_Settings__c='" + item.Id + "'";

            //Query
            var result1 = await _uniService.Query(query1);

          
            Rootlistd json11 = JsonConvert.DeserializeObject<Rootlistd>(result1);
            foreach (var record in json11.records)
            {
                bool exists1 = await _context.TermSetting.AnyAsync(t => t.Id == record.Id);
                if (!exists1)
                {
                    var termSetting = new TermSetting
                    {
                        Id = record.Id,
                        Term_Name_Formula = record.Term_Name_Formula__c,
                        Name = record.Name,
                        Semester = record.Semester__c,
                        Academic_Year = record.Academic_Year__c,
                        Term_Logo = record.Term_Logo__c,
                        Degree = record.Degree__c,
                        Active = true, // أو حسب ما بدك
                        Term_Settings = record.Term_Settings__c
                    };

                    _context.TermSetting.Add(termSetting);
                }
            }

            // حفظ الكل بعد التحقق
            await _context.SaveChangesAsync();
        }


        return Ok(new ApiResponse
        {
            Status = true,
            Code = 200,
            Message = "",
            Data = "",
            Language = "en"
        });


    }
    [HttpGet("Majorslist")]
    public async Task<ActionResult<ApiResponse>> Majorslist()
    {
        var exists = _context.TermSetting.Where(a=>a.Term_Settings!=null&&a.Active).ToList();
        foreach (var item in exists)
        {
            var responce = new Rootlist2();
            var allRecords = new List<Record8>();

            string salceidtoken = _uniService1.GetAccessToken();
            var client = new HttpClient();

            var request = new HttpRequestMessage(HttpMethod.Get,
                $"{_salesforceSettings.Value.BaseUrl}/query?q=SELECT Id,University_Name__c,Program_Degree__c,Program_Name__c,Alternative_Program_Name__c,CurrencyType__c,Tuition_Fee__c,Discounted_Tuition_Fee__c,Cash_Payment_Fee__c,Prep_School_Fee__c,Deposit_Price__c,Language__c,Campus__c,Quota_Full__c,Academic_Year__c,Program__c,Semester__c,University_Id__c,University_Account_Billing_Country__c FROM PricelistEntry__c WHERE Term_Settings__c='{item.Term_Settings}' AND Program_Degree__c='{item.Degree}' AND Term_is_Active__c=True AND Passive__c=False  ");

            request.Headers.Add("Authorization", "Bearer " + salceidtoken);
            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadAsStringAsync();
            Rootlist2 json = JsonConvert.DeserializeObject<Rootlist2>(result);
            allRecords.AddRange(json.records);

            // تابع التحميل إذا فيه nextRecordsUrl
            while (!json.done && !string.IsNullOrEmpty(json.nextRecordsUrl))
            {
                json = await _uniService.after2(json.nextRecordsUrl);
                allRecords.AddRange(json.records);
            }

            responce.totalSize = allRecords.Count;
            responce.records = allRecords;

            var universityProgramsBatch = new List<UniversityProgram>();
            int batchCounter = 0;

            foreach (var record in allRecords)
            {
                var matchingUniversity = await _context.University
                    .FirstOrDefaultAsync(u => u.SalceId == record.University_Id__c);

                string logo = matchingUniversity != null ?
                    "https://unitededucation.com/uniteduni/" + matchingUniversity.LinkLogo : "NULL";
                string universityNameAr = _contextuni.unidaynas.Where(a => a.newsid.Contains(record.University_Id__c)).Select(a => a.uniname).FirstOrDefault();
                if (record.University_Id__c == "001P200000cbN7uIAE")
                {
                    universityNameAr = "جامعة برلين للأعمال والابتكار";
                }
                else if (record.University_Id__c == "001P200000cbN7vIAE")
                {
                    universityNameAr = "جامعة كونستركتور";
                }
                else if (record.University_Id__c == "001P200000cbN7yIAE")
                {
                    universityNameAr = "جامعة جيسما للعلوم التطبيقية";
                }
                else if (record.University_Id__c == "001P200000cbN81IAE")
                {
                    universityNameAr = "جامعة برلين للعلوم التطبيقية SRH";
                }
                else if (record.University_Id__c == "0018d00000lzFFWAA2")
                {
                    universityNameAr = "Acibadem University";
                }
                else if (record.University_Id__c == "001P200000GMojbIAD")
                {
                    universityNameAr = "جامعة قبرص الدولية";
                }
                else if (record.University_Id__c == "001P200000GN1tSIAT")
                {
                    universityNameAr = "جامعة فاينل الدولية";
                }
                var universityProgram = new UniversityProgram
                {
                    Id = record.Id ?? "",
                    
                    Academic_Year__c = record.Academic_Year__c ?? "",
                    Alternative_Program_Name__c = record.Alternative_Program_Name__c ?? "",
                    Campus__c = record.Campus__c ?? "",
                    Cash_Payment_Fee__c = ParseDecimal(record.Cash_Payment_Fee__c) ?? 0,
                    CreatedById = "",
                    CurrencyType__c = record.CurrencyType__c ?? "",
                    Deposit_Price__c = ParseDecimal(record.Deposit_Price__c) ?? 0,
                    Discipline__c = "",
                    Discounted_Tuition_Fee__c = ParseDecimal(record.Discounted_Tuition_Fee__c) ?? 0,
                    Duration__c = "",
                    Faculty_Institute__c = "",
                    Language__c = record.Language__c ?? "",
                    LastModifiedById = "",
                    Old_Id__c = record.Id ?? "",
                    OwnerId = Guid.NewGuid().ToString(),
                    Passive__c = false,
                    Prep_School_Fee__c = ParseDecimal(record.Prep_School_Fee__c) ?? 0,
                    Name = record.Program_Name__c ?? "",
                    Program__c = record.Program__c ?? "",
                    Program_Degree__c = record.Program_Degree__c ?? "",
                    Program_Name__c = record.Program_Name__c ?? "",
                    Quota_Full__c = record.Quota_Full__c ,
                    Quota_of_Program__c = 0,
                    Semester__c = record.Semester__c ?? "",
                    Term_is_Active__c = true,
                    Term_Settings__c = item.Term_Settings ?? "",
                    Tuition_Fee__c = ParseDecimal(record.Tuition_Fee__c) ?? 0,
                    University_Account_Billing_Country__c = record.University_Account_Billing_Country__c ?? "",
                    University_Billing_Country__c = "",
                    University_City__c = "",
                    University_Grade__c =0,
                    University_Id__c = record.University_Id__c ?? "",
                    University_Logo__c = logo,
                    University_Maximum_Selection__c = null,
                    University_Name__c = record.University_Name__c ?? "",
                    with_Thesis_non_Thesis__c = "",
                    Year__c = "",
                    University_NameAr = universityNameAr,
                    University_NameRu = record.University_Name__c,
                    University_NameFa = record.University_Name__c,
                    ProgramFa = record.Program_Name__c,
                    ProgramRu = record.Program_Name__c
                };

                universityProgramsBatch.Add(universityProgram);
                batchCounter++;

                // كل 2000 سجل: أضفهم واحفظ
                if (batchCounter == 2000)
                {
                    _context.UniversityProgram.AddRange(universityProgramsBatch);
                    await _context.SaveChangesAsync();
                    universityProgramsBatch.Clear();
                    batchCounter = 0;
                }
            }

            // أضف الباقي إن وجد
            if (universityProgramsBatch.Count > 0)
            {
                _context.UniversityProgram.AddRange(universityProgramsBatch);
                await _context.SaveChangesAsync();
            }
        }
        return Ok(new ApiResponse
        {
            Status = true,
            Code = 200,
            Message = "Programs synced",
            Data = "",
            Language = "en"
        });
    }

    // دالة تحويل إلى Decimal?
    private decimal? ParseDecimal(string value)
    {
        return decimal.TryParse(value, out var result) ? result : (decimal?)null;
    }


}
