[{"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "PersonContactId", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "Updateaccountstudentdegree", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "Preferred_Degree__pc", "Type": "System.String", "IsRequired": false}, {"Name": "Registration_Type__pc", "Type": "System.String", "IsRequired": false}, {"Name": "accountid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "applicationlistforupload", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "termid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "RDA__cid", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Newtonsoft.Json.Linq.JArray", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "ContentDocument", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "ContentDistributionurl", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "r<PERSON><PERSON><PERSON>", "RelativePath": "api/Application", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "termid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Controllers.ApplicationController+Rootrdalist", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "applicationlist", "RelativePath": "api/Application/applicationlist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Is_Scholarship_Exist__c", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "recordsPerPage", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortColumn", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "Applicant_Name__c", "Type": "System.String", "IsRequired": false}, {"Name": "University_Name__c", "Type": "System.String", "IsRequired": false}, {"Name": "Program_Name__c", "Type": "System.String", "IsRequired": false}, {"Name": "Stage", "Type": "System.String", "IsRequired": false}, {"Name": "Mobile__c", "Type": "System.String", "IsRequired": false}, {"Name": "Offer_Letter_Link__c", "Type": "System.String", "IsRequired": false}, {"Name": "Acceptance_Letter_URL__c", "Type": "System.String", "IsRequired": false}, {"Name": "Academic_Year", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "University_PIN_Code__c", "Type": "System.String", "IsRequired": false}, {"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "Scholarship", "Type": "System.String", "IsRequired": false}, {"Name": "SubAgencyId", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationStatus", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "Degreelist", "RelativePath": "api/Application/Degreelist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "GetDocumentListAsync", "RelativePath": "api/Application/documents", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "GetBase64Data", "RelativePath": "api/Application/GetBase64Data", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "contentDocumentId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "Majorslist", "RelativePath": "api/Application/Majorslist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Degree", "Type": "System.String", "IsRequired": false}, {"Name": "destination", "Type": "System.String", "IsRequired": false}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "SubmitApp", "RelativePath": "api/Application/SubmitApp", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "data", "Type": "StudentManagementAPI.Controllers.ApplicationController+RequestData", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "Termlist", "RelativePath": "api/Application/Termlist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "updatedegree", "RelativePath": "api/Application/updatedegree", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "data", "Type": "StudentManagementAPI.Controllers.ApplicationController+RequestDatadegree", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ApplicationController", "Method": "UploadUnitedFile", "RelativePath": "api/Application/uploadunitedfile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "accountid", "Type": "System.String", "IsRequired": false}, {"Name": "appid", "Type": "System.String", "IsRequired": false}, {"Name": "filetype", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "SearchSalesforceAccount", "RelativePath": "api/Auth", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "phone", "Type": "System.String", "IsRequired": false}, {"Name": "email", "Type": "System.String", "IsRequired": false}, {"Name": "fullName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.ValueTuple`3[[System<PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "checkemail", "RelativePath": "api/Auth/checkemail", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentManagementAPI.Controllers.AuthController+phonecheck", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "checkphonenumber", "RelativePath": "api/Auth/checkphonenumber", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentManagementAPI.Controllers.AuthController+phonecheck", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "HandleExternalLoginCallback", "RelativePath": "api/Auth/external-login-callback", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": false}, {"Name": "remoteError", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "GeneratePasswordResetToken", "RelativePath": "api/Auth/generate-reset-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentManagementAPI.Controllers.AuthController+GenerateTokenRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentManagementAPI.Models.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentManagementAPI.Models.RegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentManagementAPI.Controllers.AuthController+ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "SignInWithApple", "RelativePath": "api/Auth/signin-apple", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "SignInWithFacebook", "RelativePath": "api/Auth/signin-facebook", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "SignInWithGoogle", "RelativePath": "api/Auth/signin-google", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.AuthController", "Method": "SignInWithMicrosoft", "RelativePath": "api/Auth/signin-microsoft", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "GetConsultants", "RelativePath": "api/Consultants", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[StudentManagementAPI.Models.Consultant, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "CreateConsultant", "RelativePath": "api/Consultants", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "consultant<PERSON>to", "Type": "StudentManagementAPI.Controllers.CreateConsultantDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Models.Consultant", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "GetConsultant", "RelativePath": "api/Consultants/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Models.Consultant", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "UpdateConsultant", "RelativePath": "api/Consultants/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "consultant<PERSON>to", "Type": "StudentManagementAPI.Controllers.UpdateConsultantDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "DeleteConsultant", "RelativePath": "api/Consultants/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "GetConsultantByStudentId", "RelativePath": "api/Consultants/ByStudent/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Models.Consultant", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "UpdateConsultant", "RelativePath": "api/Consultants/update-user", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "StudentManagementAPI.Controllers.ConsultantsController+UpdateConsultantRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.ConsultantsController", "Method": "UpdateStudentImage", "RelativePath": "api/Consultants/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.DatabaseController", "Method": "SearchAccount", "RelativePath": "api/Database/account/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "AgencyId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.DatabaseController", "Method": "InsertEventAttendee", "RelativePath": "api/Database/eventattendee", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "StudentManagementAPI.Controllers.EventAttendeeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.DatabaseController", "Method": "GetAccount", "RelativePath": "api/Database/getaccount", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.EventsController", "Method": "GetEvents", "RelativePath": "api/Events", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[StudentManagementAPI.Models.Dtos.EventDto, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.EventsController", "Method": "CreateEvent", "RelativePath": "api/Events", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createEventDto", "Type": "StudentManagementAPI.Models.Dtos.CreateEventDto", "IsRequired": true}], "ReturnTypes": [{"Type": "StudentManagementAPI.Models.Dtos.EventDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.EventsController", "Method": "GetEvent", "RelativePath": "api/Events/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "StudentManagementAPI.Models.Dtos.EventDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.EventsController", "Method": "UpdateEvent", "RelativePath": "api/Events/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateEventDto", "Type": "StudentManagementAPI.Models.Dtos.UpdateEventDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.EventsController", "Method": "DeleteEvent", "RelativePath": "api/Events/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.FairRegistrationsController", "Method": "InsertFairRegistration", "RelativePath": "api/FairRegistrations", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "item", "Type": "StudentManagementAPI.FairRegistrationsController+FairRegistration", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.FairRegistrationsController", "Method": "SyncRegistrations", "RelativePath": "api/FairRegistrations/sync", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.FileUploadController", "Method": "UploadFile", "RelativePath": "api/FileUpload/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.HollandCodeController", "Method": "GetHollandCodeInfo", "RelativePath": "api/HollandCode/{code}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Services.HollandCodeResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "StudentManagementAPI.Controllers.QuestionsController", "Method": "GetQuestions", "RelativePath": "api/Questions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "languages", "Type": "System.String", "IsRequired": false}, {"Name": "testAttemptId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "testType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.QuestionsController", "Method": "GetQuestion", "RelativePath": "api/Questions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "languages", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.QuestionsController", "Method": "GetActiveQuestions", "RelativePath": "api/Questions/active", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "languages", "Type": "System.String", "IsRequired": false}, {"Name": "testAttemptId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "testType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.SalesforceUsersController", "Method": "SalesforceUserlist", "RelativePath": "api/SalesforceUsers", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[StudentManagementAPI.Controllers.SalesforceUsersController+SalesforceUser, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.SalesforceUsersController", "Method": "FetchFromSalesforce", "RelativePath": "api/SalesforceUsers/insert-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[StudentManagementAPI.Controllers.SalesforceUsersController+SalesforceUser, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.SalesforceUsersController", "Method": "FetchFromSalesforceu", "RelativePath": "api/SalesforceUsers/update-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[StudentManagementAPI.Controllers.SalesforceUsersController+SalesforceUser, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.SmsController", "Method": "SendCode", "RelativePath": "api/sms/send-code", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "StudentManagementAPI.Controllers.SmsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.SmsController", "Method": "VerifyCode", "RelativePath": "api/sms/verify-code", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "StudentManagementAPI.Controllers.SmsVerificationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentsController", "Method": "GetStudents", "RelativePath": "api/Students", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[StudentManagementAPI.Models.Student, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentsController", "Method": "UpdateStudent", "RelativePath": "api/Students", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentUpdate", "Type": "StudentManagementAPI.Models.Dtos.StudentUpdateDto", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentsController", "Method": "Updateaccountstudentmajors", "RelativePath": "api/Students", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "Selected_Result_Programs__c", "Type": "System.String", "IsRequired": false}, {"Name": "accountid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentsController", "Method": "Updateaccountstudentdestination", "RelativePath": "api/Students", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "Account_Billing_Country__c", "Type": "System.String", "IsRequired": false}, {"Name": "accountid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentsController", "Method": "UpdateStudentbyid", "RelativePath": "api/Students/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "Id", "Type": "System.String", "IsRequired": false}, {"Name": "RefCode", "Type": "System.String", "IsRequired": false}, {"Name": "Point", "Type": "System.Int32", "IsRequired": false}, {"Name": "ImageURL", "Type": "System.String", "IsRequired": false}, {"Name": "FirstName", "Type": "System.String", "IsRequired": false}, {"Name": "LastName", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "DateOfBirth", "Type": "System.DateTime", "IsRequired": false}, {"Name": "CountryOfResidence", "Type": "System.String", "IsRequired": false}, {"Name": "Citizenship", "Type": "System.String", "IsRequired": false}, {"Name": "PassportNumber", "Type": "System.String", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "Mothername", "Type": "System.String", "IsRequired": false}, {"Name": "RegistrationType", "Type": "System.String", "IsRequired": false}, {"Name": "SecondarySchoolCountry", "Type": "System.String", "IsRequired": false}, {"Name": "SecondarySchoolName", "Type": "System.String", "IsRequired": false}, {"Name": "TCKimlikNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Gender", "Type": "System.String", "IsRequired": false}, {"Name": "CurrentStage", "Type": "System.String", "IsRequired": false}, {"Name": "SchoolOrUniversityName", "Type": "System.String", "IsRequired": false}, {"Name": "DestinationCountry", "Type": "System.String", "IsRequired": false}, {"Name": "DegreeInterest", "Type": "System.String", "IsRequired": false}, {"Name": "Language", "Type": "System.String", "IsRequired": false}, {"Name": "FieldOfStudyInterest", "Type": "System.String", "IsRequired": false}, {"Name": "InterestedUniversities", "Type": "System.String", "IsRequired": false}, {"Name": "EnrollmentDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "CrmId", "Type": "System.String", "IsRequired": false}, {"Name": "arrange", "Type": "System.Int32", "IsRequired": false}, {"Name": "datecreated", "Type": "System.DateTime", "IsRequired": false}, {"Name": "datemodified", "Type": "System.DateTime", "IsRequired": false}, {"Name": "IP", "Type": "System.String", "IsRequired": false}, {"Name": "modifiedIP", "Type": "System.String", "IsRequired": false}, {"Name": "lang", "Type": "System.String", "IsRequired": false}, {"Name": "user", "Type": "System.String", "IsRequired": false}, {"Name": "year", "Type": "System.String", "IsRequired": false}, {"Name": "guid", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUserId", "Type": "System.String", "IsRequired": false}, {"Name": "AddressCountry", "Type": "System.String", "IsRequired": false}, {"Name": "AddressCity", "Type": "System.String", "IsRequired": false}, {"Name": "AddressStreet", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.FirstName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.LastName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.PassportNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.FatherName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Mothername", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.RegistrationType", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.SecondarySchoolCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.SecondarySchoolName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.TCKimlikNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.CrmId", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.CurrentStage", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.SchoolOrUniversityName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.DestinationCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.DegreeInterest", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Language", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.FieldOfStudyInterest", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.InterestedUniversities", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Gender", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.DateOfBirth", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.CountryOfResidence", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Citizenship", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Id", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.RefCode", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Point", "Type": "System.Int32", "IsRequired": false}, {"Name": "ApplicationUser.Student.ImageURL", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.FirstName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.LastName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Email", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.DateOfBirth", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.CountryOfResidence", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Citizenship", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.PassportNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Mothername", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.RegistrationType", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.SecondarySchoolCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.SecondarySchoolName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.TCKimlikNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Gender", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.CurrentStage", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.SchoolOrUniversityName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.DestinationCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.DegreeInterest", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Language", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.FieldOfStudyInterest", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.InterestedUniversities", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.EnrollmentDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.CrmId", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.arrange", "Type": "System.Int32", "IsRequired": false}, {"Name": "ApplicationUser.Student.datecreated", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.datemodified", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.IP", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.modifiedIP", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.lang", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.user", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.year", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.guid", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUserId", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.AddressCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.AddressCity", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.AddressStreet", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.FirstName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.LastName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.PassportNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.FatherName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Mothername", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.RegistrationType", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.SecondarySchoolCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.SecondarySchoolName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.TCKimlikNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.CrmId", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.CurrentStage", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.SchoolOrUniversityName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.DestinationCountry", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.DegreeInterest", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Language", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.FieldOfStudyInterest", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.InterestedUniversities", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Gender", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.DateOfBirth", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.CountryOfResidence", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Citizenship", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Student", "Type": "StudentManagementAPI.Models.Student", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Id", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.UserName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.NormalizedUserName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.Email", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.NormalizedEmail", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.EmailConfirmed", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.PasswordHash", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.SecurityStamp", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.ConcurrencyStamp", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.PhoneNumberConfirmed", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.TwoFactorEnabled", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.LockoutEnd", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.LockoutEnabled", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.Student.ApplicationUser.AccessFailedCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "ApplicationUser.Student.ConsultantId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.FirstName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.LastName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.ImageURL", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.Description", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.SalesforceId", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.Email", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.DateCreated", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.DateModified", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ApplicationUser.Student.Consultant.Students", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.Student, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ApplicationUser.Student.TestAttempts", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.TestAttempt, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ApplicationUser.Student.ReferralsMade", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.Referral, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ApplicationUser.Student.ReferralsReceived", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.Referral, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ApplicationUser.Id", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.UserName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.NormalizedUserName", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.Email", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.NormalizedEmail", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.EmailConfirmed", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.PasswordHash", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.SecurityStamp", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.ConcurrencyStamp", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "ApplicationUser.PhoneNumberConfirmed", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.TwoFactorEnabled", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.LockoutEnd", "Type": "System.Nullable`1[[System.DateTimeOffset, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ApplicationUser.LockoutEnabled", "Type": "System.Boolean", "IsRequired": false}, {"Name": "ApplicationUser.AccessFailedCount", "Type": "System.Int32", "IsRequired": false}, {"Name": "ConsultantId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Consultant.Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "Consultant.FirstName", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.LastName", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.ImageURL", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.Description", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.SalesforceId", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.Email", "Type": "System.String", "IsRequired": false}, {"Name": "Consultant.DateCreated", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Consultant.DateModified", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Consultant.Students", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.Student, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "TestAttempts", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.TestAttempt, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ReferralsMade", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.Referral, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "ReferralsReceived", "Type": "System.Collections.Generic.ICollection`1[[StudentManagementAPI.Models.Referral, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": false}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentsController", "Method": "DeleteStudent", "RelativePath": "api/Students/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentsController", "Method": "GetStudent", "RelativePath": "api/Students/byid", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Models.Student", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentsController", "Method": "ResetPassword", "RelativePath": "api/Students/Change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "StudentsController+changePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentsController", "Method": "updatemajors", "RelativePath": "api/Students/updatemajors", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "data", "Type": "StudentManagementAPI.Controllers.ApplicationController+RequestDatamajors", "IsRequired": true}], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentsController", "Method": "UpdateStudentcosultant", "RelativePath": "api/Students/UpdateStudentcosultant", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "accountid", "Type": "System.String", "IsRequired": false}, {"Name": "owenerid", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentsController", "Method": "UpdateStudentImage", "RelativePath": "api/Students/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "GetTestAttempts", "RelativePath": "api/TestAttempts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[StudentManagementAPI.Models.TestAttempt, StudentManagementAPI, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "PersonContactId", "RelativePath": "api/TestAttempts", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "leadId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "inserttoholland", "RelativePath": "api/TestAttempts", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "Selected_Result_Programs__c", "Type": "System.String", "IsRequired": false}, {"Name": "ContactId__c", "Type": "System.String", "IsRequired": false}, {"Name": "Result_Code__c", "Type": "System.String", "IsRequired": false}, {"Name": "Result_Programs__c", "Type": "System.String", "IsRequired": false}, {"Name": "External_Id__c", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "updateholland", "RelativePath": "api/TestAttempts", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "Result_Code__c", "Type": "System.String", "IsRequired": false}, {"Name": "Result_Programs__c", "Type": "System.String", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "GetTestAttempt", "RelativePath": "api/TestAttempts/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "testType", "Type": "System.String", "IsRequired": false}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "UpdateTestName", "RelativePath": "api/TestAttempts/{id}/name", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "PauseTestAttempt", "RelativePath": "api/TestAttempts/{id}/pause", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "GetTestQuestions", "RelativePath": "api/TestAttempts/{id}/questions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Controllers.TestAttemptsController+ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "ResumeTestAttempt", "RelativePath": "api/TestAttempts/{id}/resume", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptAnswersController", "Method": "UpdateAnswer", "RelativePath": "api/testattempts/{testAttemptId}/answers/{answerId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "testAttemptId", "Type": "System.Int32", "IsRequired": true}, {"Name": "answerId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "StudentManagementAPI.Models.AnswerUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptAnswersController", "Method": "SubmitBatchAnswers", "RelativePath": "api/testattempts/{testAttemptId}/answers/batch", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "testAttemptId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "StudentManagementAPI.Models.BatchAnswerRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "GetResults", "RelativePath": "api/TestAttempts/results/{testAttemptId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "testAttemptId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "UpdateAnswer", "RelativePath": "api/TestAttempts/SaveAnswer", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "StudentManagementAPI.Controllers.TestAttemptsController+UpdateAnswerRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "StudentManagementAPI.Controllers.TestAttemptsController", "Method": "StartTestAttempt", "RelativePath": "api/TestAttempts/start", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "StudentManagementAPI.Controllers.TestAttemptsController+StartTestAttemptRequest", "IsRequired": true}, {"Name": "lang", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "StudentManagementAPI.Controllers.TestAttemptsController+ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UniversityProgramsController", "Method": "Degreelist", "RelativePath": "api/UniversityPrograms/Degreelist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UniversityProgramsController", "Method": "InsertUniversityProgram", "RelativePath": "api/UniversityPrograms/InsertUniversityProgram", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "program", "Type": "StudentManagementAPI.Models.UniversityProgram", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "UniversityProgramsController", "Method": "Majorslist", "RelativePath": "api/UniversityPrograms/Majorslist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "UniversityProgramsController", "Method": "Termlist", "RelativePath": "api/UniversityPrograms/Termlist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]