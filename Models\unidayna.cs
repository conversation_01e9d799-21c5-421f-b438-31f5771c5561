﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StudentManagementAPI.Models
{
    [Table("unidayna")]
    public partial class unidayna
    {
        public int Id { get; set; }

        public string uniname { get; set; }

        public string linkphoto { get; set; }

        public string linklogo { get; set; }

        public string constr { get; set; }

        public string studentcount { get; set; }

        public string specia { get; set; }

        public string ser1 { get; set; }

        public string ser2 { get; set; }

        public string ser3 { get; set; }

        public string ser4 { get; set; }

        public string ser5 { get; set; }

        public string ser6 { get; set; }

        public string ser7 { get; set; }

        public int? index1 { get; set; }

        public string selected { get; set; }

        public string uninamel { get; set; }

        public string ser8 { get; set; }

        public int visi { get; set; }

        public int comm { get; set; }

        public string rate { get; set; }

        public string vedindex { get; set; }

        [StringLength(50)]
        public string infirstpage { get; set; }

        public string mdes { get; set; }

        public string mkeyword { get; set; }

        public string vmap { get; set; }

        public string unicode { get; set; }

        public string unilevel { get; set; }

        public string city { get; set; }

        public string enname { get; set; }

        public string otitle { get; set; }
        public string otitleen { get; set; }
        public string mdesen { get; set; }
        public string ispub { get; set; }
        public string mkeyworden { get; set; }
        public string otitlefr { get; set; }
        public string mdesfr { get; set; }

        public string mkeywordfr { get; set; }
        public string otitleru { get; set; }
        public string mdesru { get; set; }

        public string mkeywordru { get; set; }
        public string ins { get; set; }

        public string shome { get; set; }

        public string adv { get; set; }
        public int? cola { get; set; }
        public int? mbaa { get; set; }
        public int? phda { get; set; }
        public int? insta { get; set; }

        public int? adva { get; set; }
        public int? mapa { get; set; }
        [NotMapped]
        public bool IsSelected { get; set; }
        public bool isfa { get; set; } = false;
        public bool isru { get; set; } = false;

        public string newsid { get; set; }
    }
}
