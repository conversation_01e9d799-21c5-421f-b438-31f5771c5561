﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace StudentManagementAPI.Migrations.UniversityProgramsDb
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UniversityProgram",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Academic_Year__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Alternative_Program_Name__c = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Campus__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Cash_Payment_Fee__c = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    CreatedById = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    CurrencyType__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Deposit_Price__c = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Discipline__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Discounted_Tuition_Fee__c = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Duration__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Faculty_Institute__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Language__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LastModifiedById = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Old_Id__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    OwnerId = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Passive__c = table.Column<bool>(type: "bit", nullable: true),
                    Prep_School_Fee__c = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Program__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Program_Degree__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Program_Name__c = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Quota_Full__c = table.Column<int>(type: "int", nullable: true),
                    Quota_of_Program__c = table.Column<int>(type: "int", nullable: true),
                    Semester__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Term_is_Active__c = table.Column<bool>(type: "bit", nullable: true),
                    Term_Settings__c = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Tuition_Fee__c = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    University_Account_Billing_Country__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    University_Billing_Country__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    University_City__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    University_Grade__c = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    University_Id__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    University_Logo__c = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    University_Maximum_Selection__c = table.Column<int>(type: "int", nullable: true),
                    University_Name__c = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    with_Thesis_non_Thesis__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Year__c = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UniversityProgram", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UniversityProgram");
        }
    }
}
