using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Models;

namespace StudentManagementAPI.Data
{
    public class sit : DbContext
    {
        public sit(DbContextOptions<sit> options)
            : base(options)
        {
        }
        public DbSet<clist> clists { get; set; }
        public virtual DbSet<unidayna> unidaynas { get; set; }
        public DbSet<ApiRequestLognewapi> apiRequestLognewapis { get; set; }
        // All DbSets have been moved to ApplicationDbContext

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            
            // This context is now empty as all entities have been moved to ApplicationDbContext
        }
    }
}