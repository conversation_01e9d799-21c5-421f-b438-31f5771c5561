using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Data;
using StudentManagementAPI.Helpers;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using StudentManagementAPI.Services;
using System.Security.Claims;
using System.Text.Json;
using System.Text.Json.Serialization;
using static StudentManagementAPI.service.getuni2023;
using static UniversityProgramsController;

namespace StudentManagementAPI.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ApplicationController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UniversityProgramsDbContext _contextuni;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILanguageService _languageService;
        private readonly RsaHelper _uniService1;
        private readonly ILogger<ApplicationController> _logger;
        private readonly IOptions<SalesforceSettings> _salesforceSettings;
        // Supported languages
        private readonly string[] _supportedLanguages = new[] { "en", "ar", "ru", "fa" };
        private const string DEFAULT_LANGUAGE = "en";

        public ApplicationController(
            ApplicationDbContext context,
            UniversityProgramsDbContext contextuni,
            UserManager<ApplicationUser> userManager, 
            ILanguageService languageService, 
            getuni2023 uniService, 
            RsaHelper uniService1,
            ILogger<ApplicationController> logger,
            IOptions<SalesforceSettings> salesforceSettings)
        {
            _contextuni = contextuni;
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _languageService = languageService ?? throw new ArgumentNullException(nameof(languageService));
            _uniService = uniService;
            _uniService1 = uniService1;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _salesforceSettings = salesforceSettings ?? throw new ArgumentNullException(nameof(salesforceSettings));
        }
        private readonly getuni2023 _uniService;
        private string ValidateLanguage(string lang)
        {
            if (string.IsNullOrEmpty(lang) || !_supportedLanguages.Contains(lang.ToLower()))
            {
                return DEFAULT_LANGUAGE;
            }
            return lang.ToLower();
        }
        private async Task<string> GetCurrentStudentId()
        {
            if (!User.Identity.IsAuthenticated)
                return null;

            var username = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(username))
                return null;

            var user = await _userManager.FindByNameAsync(username); // ✅ التصليحة هون

            if (user == null)
                return null;

            var student = await _context.Students
                .FirstOrDefaultAsync(s => s.Id == user.Id);

            return student?.Id;
        }
        public class Rootlist1
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public string nextRecordsUrl { get; set; }

            public List<Recordp> records { get; set; }
        }
        public class Recordp
        {
            public string Semester__c { get; set; }
            public string Id { get; set; }
            public string Term_Name_Formula__c { get; set; }

            public string Academic_Year__c { get; set; }

        }

        [HttpGet("Termlist")]
        public async Task<ActionResult<ApiResponse>> Termlist([FromQuery]  string lang = DEFAULT_LANGUAGE)
        {
            string language = !string.IsNullOrEmpty(lang) ? lang : lang;
            language = ValidateLanguage(language);

            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language),
                    Language = language
                });
            }
            //prepare the query
            // Query your local database using Entity Framework
            var records = await _contextuni.TermSetting.Where(a=>a.Term_Settings==null).ToListAsync();

            // Map to API format if needed (e.g., match the structure of Rootlist1)
            var response = new Rootlist1
            {
                totalSize = records.Count,
                done = true, // You can modify this if you paginate
                records = records.Select(r => new Recordp
                {
                    Id = r.Id,
                    Term_Name_Formula__c = r.Term_Name_Formula,
                    Semester__c = r.Semester,
                    Academic_Year__c = r.Academic_Year
                }).ToList()
            };

            // Return the result
            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = "",
                Data = response,
                Language = language
            });

        }
        [HttpGet("Degreelist")]
        public async Task<ActionResult<ApiResponse>> Degreelist( [FromQuery]string Termid, string lang = DEFAULT_LANGUAGE)
        {
            string language = !string.IsNullOrEmpty(lang) ?lang : lang;
            language = ValidateLanguage(language);

            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language),
                    Language = language
                });
            }

            var records = await _contextuni.TermSetting
      .Where(x => x.Term_Settings == Termid)
      .ToListAsync();


            // Map to API format if needed (e.g., match the structure of Rootlist1)
            var result = new Rootlistd
            {
                totalSize = records.Count,
                done = true,
                records = records.Select(r => new Recordd
                {
                    Id = r.Id,
                    Term_Name_Formula__c = r.Term_Name_Formula,
                    Name = r.Name,
                    Semester__c = r.Semester,
                    Academic_Year__c = r.Academic_Year,
                    Term_Logo__c = r.Term_Logo,
                    Degree__c = r.Degree
                }).ToList()
            };


            // Return the result
            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = "",
                Data = result,
                Language = language
            });
        }
        public class Rootlist2
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public string nextRecordsUrl { get; set; }
            public List<Record8> records { get; set; }

        }
        public class Record8
        {
            public string Id { get; set; }
            public string University_Name__c { get; set; }
            public string Program_Degree__c { get; set; }
            public string Program_Name__c { get; set; }
            public string University_Name__cn { get; set; }
            public string University_NameAr { get; set; }
            public string University_NameRu { get; set; }
            public string University_NameFa { get; set; }
            public string Program_Name__cn { get; set; }
            public string Alternative_Program_Name__c { get; set; }
            public string CurrencyType__c { get; set; }
            public string Tuition_Fee__c { get; set; }
            public string Discounted_Tuition_Fee__c { get; set; }
            public string Cash_Payment_Fee__c { get; set; }
            public string Prep_School_Fee__c { get; set; }
            public string Deposit_Price__c { get; set; }
            public string Language__c { get; set; }
            public string Campus__c { get; set; }
            public bool Quota_Full__c { get; set; }
            public string Program__c { get; set; }
            public string ProgramFa { get; set; }
            public string ProgramRu { get; set; }
            public string Term_Settings__c { get; set; }
            public string Semester__c { get; set; }
            public string University_Id__c { get; set; }
            public string Academic_Year__c { get; set; }
            public string University_Account_Billing_Country__c { get; set; }
            public string unilogo { get; set; }

        }
        [HttpGet("Majorslist")]
        public async Task<ActionResult<ApiResponse>> Majorslist(
      [FromQuery] string Termid,
      string Degree,string? destination,
      string lang = DEFAULT_LANGUAGE)
        {
            var resultList = await _contextuni.UniversityProgram
    .Where(x =>
        x.Term_Settings__c == Termid &&
        x.Program_Degree__c == Degree &&
        x.Term_is_Active__c == true &&
        x.Passive__c == false &&
        (string.IsNullOrEmpty(destination) || x.University_Account_Billing_Country__c == destination)
    )
    .Select(e => new
    {
        e.Id,
        e.Academic_Year__c,
        Alternative_Program_Name__c = e.Alternative_Program_Name__c ?? "",
        Campus__c = e.Campus__c ?? "",
        Cash_Payment_Fee__c = e.Cash_Payment_Fee__c ?? 0,
        CurrencyType__c = e.CurrencyType__c ?? "",
        Deposit_Price__c = e.Deposit_Price__c ?? 0,
        Language__c = e.Language__c ?? "",
        Prep_School_Fee__c = e.Prep_School_Fee__c ?? 0,
        Program__c = e.Program__c ?? "",
        Program_Degree__c = e.Program_Degree__c ?? "",
        Program_Name__c = e.Program_Name__c ?? "",
        Semester__c = e.Semester__c ?? "",
        Tuition_Fee__c = e.Tuition_Fee__c ?? 0,
        University_Id__c = e.University_Id__c ?? "",
        University_Name__c = e.University_Name__c ?? "",
        University_Account_Billing_Country__c = e.University_Account_Billing_Country__c ?? "",
        ProgramFa = e.ProgramFa ?? "",
        ProgramRu = e.ProgramRu ?? "",
        University_NameAr = e.University_NameAr ?? "",
        University_NameRu = e.University_NameRu ?? "",
        University_NameFa = e.University_NameFa ?? "",
        Unilogo = string.IsNullOrEmpty(e.University_Logo__c) ? "NULL" : e.University_Logo__c
    })
    .ToListAsync();



            // إنشاء الرد النهائي
            var response = new
            {
                totalSize = resultList.Count,
                records = resultList
            };

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = "",
                Data = response,
                Language = lang
            });
        }


        public class RequestDatadegree
        {
            // حقول عامة من body
            public string PreferredDegree { get; set; }

            public string RegistrationType { get; set; }
            public string Accountid { get; set; }

        }
        public class RequestDatamajors
        {
            // حقول عامة من body
            public string Preferredmajors { get; set; }

            public string destination { get; set; }
            public int TestAttemptId { get; set; }

        }
        public class RequestData
        {
            // حقول عامة من body
            public string? AddressCountry { get; set; }
            public string? AddressCity { get; set; }
            public string? AddressStreet { get; set; }
            public string PreferredDegree { get; set; }
            public string CountryCode { get; set; }
            public string RegistrationType { get; set; }
            public string Mothername { get; set; }
            public string Passport { get; set; }
            public string Fathername { get; set; }
            public string School { get; set; }
            public string Gender { get; set; }
            public string DateOfBirth { get; set; } // إذا جاي ستيرنغ من JSON، parser بالـ SubmitApp بيحاول يحوّله DateTime. 
            public string Accountid { get; set; }   // data.accountid
            public string Email { get; set; }       // data.email

            // CheckedItems: لائحة البرامج/جامعات مختارة
            public List<CheckedItem> CheckedItems { get; set; }
        }

        public class CheckedItem
        {
            // الحقول حسب JSON من الواجهة
            public string University_Id__c { get; set; }
            public string Academic_Year__c { get; set; }
            public string Program_Degree__c { get; set; }
            public string Semester__c { get; set; }
            public string Term_Settings__c { get; set; }
            public string Id { get; set; }
            public string Program__c { get; set; }
            public string Deposit_Price__c { get; set; }
            public string University_Name__c { get; set; }
            public string CurrencyType__c { get; set; }
        }

        public string PersonContactId(string leadId)
        {

            //prepare the query
            string query = "SELECT PersonContactId   FROM Account WHERE  Id= '" + leadId + "'";

            //Query
            var result = _uniService.Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["PersonContactId"];

            return value;
        }
        [HttpPost("updatedegree")]
        public async Task<ActionResult<ApiResponse>> updatedegree([FromBody] RequestDatadegree data, [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", lang),
                    Language = lang
                });
            }
            Student student = await _context.Students
             .FirstOrDefaultAsync(s => s.Id == studentId);



            string appid = "";
            string color = "0";
            string salceid11 = "";
            string PersonContactId1 = "";
            if (data.Accountid == null)
            {


                salceid11 = student.CrmId;
                PersonContactId1 = PersonContactId(salceid11);
            }
            else
            {
                salceid11 = data.Accountid;
                PersonContactId1 = PersonContactId(salceid11);
            }
            salceid11 = data.Accountid;
            PersonContactId1 = PersonContactId1;
            await Updateaccountstudentdegree(data.PreferredDegree, data.RegistrationType, data.Accountid);
            if (student != null)
            {
                student.DegreeInterest = data.PreferredDegree;

                student.RegistrationType = data.RegistrationType;
                _context.Students.Update(student);
                await _context.SaveChangesAsync();

            }
            return Ok(new
            {
                msg = "ok",

                contactid = PersonContactId1,
                accountid = salceid11,
                appid = appid
            });






        }
        public class Recordapp
        {
            public Attributes attributes { get; set; }
            public string Id { get; set; }
            public string Applicant_Name__c { get; set; }
            public string Mobile__c { get; set; }
            public string Student_Email__c { get; set; }
            public object Stage__c { get; set; }
            public object University_Name__c { get; set; }
            public string Program_Name__c { get; set; }
            public object Academic_Year__c { get; set; }
            public object Semester__c { get; set; }
            public object University_PIN_Code__c { get; set; }
            public object Offer_Letter_Link__c { get; set; }
            public object Acceptance_Letter_URL__c { get; set; }
            public string Name { get; set; }
            public string Applicant_PA__c { get; set; }
            public string Degree__c { get; set; }
            public string Registration_Type__c { get; set; }
            public string Scholarship__c { get; set; }
            public bool Is_Scholarship_Exist__c { get; set; }
            public string ApplicationStatus__c { get; set; }
            public string Application_Status_Note__c { get; set; }
            public string Registration_Letter_Rejected_Reason__c { get; set; }
            public string Registration_Letter_Status__c { get; set; }
            public string Account__c { get; set; }
            public string Sub_Agency_Id__c { get; set; }
            public string unilogo { get; set; }
            public string Discounted_Tuition_Fee__c { get; set; }
            public string Deposit_Fee__c { get; set; }
            public string Cash_Payment_Fee__c { get; set; }
            public string Missing_Documents__c { get; set; }
            public string University__c { get; set; }


        }

        public class Rootapp
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public List<Recordapp> records { get; set; }
        }
        [HttpGet("applicationlist")]
        public async Task<ActionResult> applicationlist( bool? Is_Scholarship_Exist__c, int? page = 1, int recordsPerPage = 20, string sortColumn = "CreatedDate", string sortDirection = "DESC",
     string Applicant_Name__c = "", string University_Name__c = "", string Program_Name__c = "", string Stage = "",
     string Mobile__c = "", string Offer_Letter_Link__c = "", string Acceptance_Letter_URL__c = "",
     string Academic_Year = "", string Semester = "", string University_PIN_Code__c = "", string Id = "", string Scholarship = "", string SubAgencyId = "", string ApplicationStatus = "")
        {
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse

                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", "en"),
                    Language = "en"
                });
            }
            string accountid = await _context.Students
                .Where(s => s.Id == studentId)
                .Select(s => s.CrmId)
                .FirstOrDefaultAsync();
            string contactid = PersonContactId(accountid);
            string salceidtoken = _uniService1.GetAccessToken();
            var client = new HttpClient();
            string cmd = "";
            string cmd1 = "";


            cmd = "SELECT Id,Applicant_Name__c,Mobile__c,Student_Email__c,Stage__c,University_Name__c,Program_Name__c,Academic_Year__c,Semester__c,University_PIN_Code__c,Offer_Letter_Link__c,Acceptance_Letter_URL__c,Name,Scholarship__c,Is_Scholarship_Exist__c,Account__c,Sub_Agency_Id__c,ApplicationStatus__c,Discounted_Tuition_Fee__c,University__c  FROM Application__c where Applicant_PA__c ='" + contactid + "'";


            cmd1 = "SELECT Count(Id)   FROM Application__c WHERE  Applicant_PA__c ='" + contactid + "'  ";



            // Add filters
            if (!string.IsNullOrEmpty(Applicant_Name__c))
            {
                cmd += $" AND Applicant_Name__c LIKE '%{Applicant_Name__c}%'"; cmd1 += $" AND Applicant_Name__c LIKE '%{Applicant_Name__c}%'";
            }
            if (!string.IsNullOrEmpty(University_Name__c))
            {
                cmd += $" AND University_Name__c LIKE '%{University_Name__c}%'"; cmd1 += $" AND University_Name__c LIKE '%{University_Name__c}%'";
            }
            if (!string.IsNullOrEmpty(Program_Name__c))
            {
                cmd += $" AND Program_Name__c LIKE '%{Program_Name__c}%'"; cmd1 += $" AND Program_Name__c LIKE '%{Program_Name__c}%'";
            }
            if (!string.IsNullOrEmpty(Stage))
            {
                if (Stage == "Rejected")
                {
                    cmd += $" ApplicationStatus__c!=null ";
                    cmd1 += $" ApplicationStatus__c!=null ";
                }
                else
                {
                    cmd += $" AND Stage__c ='{Stage}'"; cmd1 += $" AND Stage__c ='{Stage}'";
                }

            }
            if (!string.IsNullOrEmpty(ApplicationStatus))
            {
                cmd += $" AND ApplicationStatus__c = '{ApplicationStatus}'"; cmd1 += $" AND ApplicationStatus__c = '{ApplicationStatus}'";
            }
            if (!string.IsNullOrEmpty(Mobile__c))
            {
                cmd += $" AND Mobile__c LIKE '%{Mobile__c}%'"; cmd1 += $" AND Mobile__c LIKE '%{Mobile__c}%'";
            }
            if (!string.IsNullOrEmpty(Offer_Letter_Link__c) && !string.IsNullOrEmpty(Acceptance_Letter_URL__c))
            {
                if (!string.IsNullOrEmpty(Stage))
                {
                    cmd += " OR (Stage__c = 'Payment' OR Stage__c = 'Pre-Registered')";
                    cmd1 += " OR (Stage__c = 'Payment' OR Stage__c = 'Pre-Registered')";

                }
                else
                {
                    cmd += " AND (Stage__c = 'Payment' OR Stage__c = 'Pre-Registered')";
                    cmd1 += " AND (Stage__c = 'Payment' OR Stage__c = 'Pre-Registered')";
                }
            }

            if (!string.IsNullOrEmpty(Offer_Letter_Link__c) && string.IsNullOrEmpty(Acceptance_Letter_URL__c))
            {
                if (!string.IsNullOrEmpty(Stage))
                {
                    cmd += " OR Stage__c ='Payment' ";
                    cmd1 += " OR Stage__c ='Payment' ";
                }
                else
                {
                    cmd += " AND Stage__c ='Payment' ";
                    cmd1 += " AND Stage__c ='Payment' ";
                }

            }
            if (!string.IsNullOrEmpty(Acceptance_Letter_URL__c) && string.IsNullOrEmpty(Offer_Letter_Link__c))
            {
                if (!string.IsNullOrEmpty(Stage))
                {
                    cmd += $" OR Stage__c  ='Pre-Registered' ";
                    cmd1 += $" OR Stage__c  ='Pre-Registered' ";
                }
                else
                {
                    cmd += $" AND Stage__c  ='Pre-Registered' ";
                    cmd1 += $" AND Stage__c  ='Pre-Registered' ";
                }

            }
            if (!string.IsNullOrEmpty(Academic_Year))
            {
                cmd += $" AND Academic_Year__c = '{Academic_Year}'"; cmd1 += $" AND Academic_Year__c = '{Academic_Year}'";
            }
            if (!string.IsNullOrEmpty(Semester))
            {
                cmd += $" AND Semester__c  = '{Semester}'"; cmd1 += $" AND Semester__c  = '{Semester}'";
            }
            if (!string.IsNullOrEmpty(University_PIN_Code__c))
            {
                cmd += $" AND University_PIN_Code__c LIKE '%{University_PIN_Code__c}%'"; cmd1 += $" AND University_PIN_Code__c LIKE '%{University_PIN_Code__c}%'";
            }
            if (!string.IsNullOrEmpty(Scholarship))
            {
                cmd += $" AND Scholarship__c !=null "; cmd1 += $" AND Scholarship__c !=null ";
            }
            if (!string.IsNullOrEmpty(SubAgencyId))
            {
                cmd += $" AND Sub_Agency_Id__c  = '{SubAgencyId}' "; cmd1 += $" AND Sub_Agency_Id__c  = '{SubAgencyId}' ";
            }



            if (!string.IsNullOrEmpty(Id))
            {
                cmd += $" AND Name LIKE '%{Id}%'";
                cmd1 += $" AND Name LIKE '%{Id}%'";
            }

            // Add sorting
            if (sortColumn == "")
            {
                sortColumn = "CreatedDate";
            }

            cmd += $" ORDER BY {sortColumn} {sortDirection}";

            // Add pagination
            int offset = (recordsPerPage * (page.Value - 1));
            cmd += $" LIMIT {recordsPerPage} OFFSET {offset}";
            // Make the HTTP request to Salesforce
            var request = new HttpRequestMessage(HttpMethod.Get, $"{_salesforceSettings.Value.BaseUrl}/query?q={Uri.EscapeDataString(cmd)}");
            request.Headers.Add("Authorization", "Bearer " + salceidtoken);

            var response = await client.SendAsync(request);
            response.EnsureSuccessStatusCode();

            var result = await response.Content.ReadAsStringAsync();

            Rootapp json = JsonConvert.DeserializeObject<Rootapp>(result);
            foreach (var record in json.records)
            {
                var matchingUniversity = await _contextuni.University.FirstOrDefaultAsync(u => u.SalceId == record.University__c);
                if (matchingUniversity != null)
                {
                    record.unilogo = "https://unitededucation.com/uniteduni/" + matchingUniversity.LinkLogo;
                }
                else
                {
                    record.unilogo = "NULL"; // Default value if no match is found
                }
            }
            var result1 = _uniService.Query(cmd1);

            JObject json1 = JObject.Parse(result1.Result);
            int value = (Int16)json1["records"][0]["expr0"];
            json.totalSize = value;
            return new JsonResult(json)
            {
                // حذف SerializerSettings، لأنه System.Text.Json ما بيدعمه بهالشكل
            };

        }
        [HttpPost("SubmitApp")]
        public async Task<ActionResult<ApiResponse>> SubmitApp([FromBody] RequestData data, [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse

                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", lang),
                    Language = lang
                });
            }
            Student student = await _context.Students
             .FirstOrDefaultAsync(s => s.Id == studentId);

             

            string appid = "";
            string color = "0";
            string salceid11 = "";
            string PersonContactId1 = "";
            if (data.Accountid == null)
            {


                salceid11 = student.CrmId;
                PersonContactId1 = PersonContactId(salceid11);
            }
            else
            {
                salceid11 = data.Accountid;
                PersonContactId1 = PersonContactId(salceid11);
            }
            salceid11 = data.Accountid;
            PersonContactId1 = PersonContactId1;
            Updateaccountstudent(data.PreferredDegree, data.CountryCode, data.RegistrationType, data.Mothername, data.Passport, data.Fathername, _uniService.Countryen(data.School), data.Gender, data.DateOfBirth, salceid11,data.AddressCity, _uniService.Countryen(data.AddressCountry),data.AddressStreet);
            if (student != null)
            {
                student.DegreeInterest = data.PreferredDegree;
                student.DestinationCountry = data.CountryCode;
                student.RegistrationType = data.RegistrationType;
                student.Mothername = data.Mothername;
                student.PassportNumber = data.Passport;
                student.FatherName = data.Fathername;
                student.SchoolOrUniversityName = data.School;
                student.Gender = data.Gender;
            

                if (!string.IsNullOrEmpty(data.DateOfBirth) && DateTime.TryParse(data.DateOfBirth, out DateTime parsedDateOfBirth))
                {
                    student.DateOfBirth = parsedDateOfBirth;
                }
                else
                {
                    // Handle the case where dateofbirth is null or invalid
                    student.DateOfBirth = default; // Assign a default value or handle as needed
                }
            
            

                _context.Students.Update(student);
                await _context.SaveChangesAsync();
            }



            data.CheckedItems = data.CheckedItems
    .GroupBy(x => x.University_Id__c)
    .Select(g => g.First())
    .ToList();

            foreach (var item in data.CheckedItems)
            {


                // string LeadId = t.CreateLeadAndGetId(name, lastname, phone1, email, y.countryen(resdance), gender, y.countryen(nat), message, folloto, null, null, our, y.levelsen(learning), y.langsen(lang), col, passid, birth, father, mother);
                var responseFromServer11 = insertapplication(item.Academic_Year__c, PersonContactId1, item.Program_Degree__c, item.Semester__c, item.Term_Settings__c, item.University_Id__c, item.Id, item.Program__c, data.Email);
                JObject jsonObject11 = JObject.Parse(responseFromServer11.Result.ToString());

                string salceid = (string)jsonObject11["id"];
                appid += salceid + ",";

                var responseFromServer1 = insertappliedprogram(item.Id, salceid);
                if (item.Deposit_Price__c != "null")
                {
                    var responseFromServer2 = insertinvoice(item.Deposit_Price__c, salceid, item.University_Name__c, salceid11, item.CurrencyType__c);
                    JObject jsonObject1 = JObject.Parse(responseFromServer2.Result.ToString());

                    string invoiceid = (string)jsonObject1["id"];
                    var tt = updateappinvoice(invoiceid, salceid);
                }
             var tt1 = updateappstage(salceid); 

                var tt2 = updatecontact(PersonContactId1);
            }

            return Ok(new
            {
                msg = "ok",

                contactid = PersonContactId1,
                accountid = salceid11,
                appid = appid
            });






        }
        async Task<string> Updateaccountstudent(string Preferred_Degree__pc, string Country_Phone_Code__pc1, string Registration_Type__pc, string Mother_Name__pc, string Passport__pc, string Father_Name__pc, string Country_of_Secondary_School__pc, string Gender__pc, string PersonBirthdate, string accountid,string Address_City__pc
,string Address_Country__pc,string Address_Street__pc)
        {

            string sObject = "Account";
            object body = new object();


            body = new
            {
                PersonBirthdate = PersonBirthdate,
                Country_of_Secondary_School__pc = Country_of_Secondary_School__pc,

                Gender__pc = Gender__pc,
                Father_Name__pc = Father_Name__pc,
                Mother_Name__pc = Mother_Name__pc,
                Passport__pc = Passport__pc,
                RecordTypeId = "0128d000000Z2vvAAC",

                Address_City__pc= Address_City__pc,

                Address_Country__pc= Address_Country__pc,
                Address_Street__pc= Address_Street__pc, 


                Country_Phone_Code__pc = Country_Phone_Code__pc1,



                Preferred_Degree__pc = Preferred_Degree__pc,

                Branch__c = "United Main",

                Registration_Type__pc = Registration_Type__pc,

                OwnerId = "0054L000001IJ70QAG",

            };



            //update
            return await _uniService.Update(sObject, accountid, body);
        }
        public async Task<string> Updateaccountstudentdegree(string Preferred_Degree__pc, string Registration_Type__pc, string accountid)
        {

            string sObject = "Account";
            object body = new object();


            body = new
            {
             



                Preferred_Degree__pc = Preferred_Degree__pc,

              
                Registration_Type__pc = Registration_Type__pc,

               

            };



            //update
            return await _uniService.Update(sObject, accountid, body);
        }
        async Task<string> insertapplication(string Academic_Year__c, string Applicant_PA__c, string Degree__c, string Semester__c, string Term_Settings__c, string University__c, string AdmittedPrice_List_Entry__c, string Admitted_Program__c, string Student_Email__c)
        {
          

            string sObject = "Application__c";
            object body = new object();


            body = new
            {
              
                AdmittedPrice_List_Entry__c = AdmittedPrice_List_Entry__c,
                Admitted_Program__c = Admitted_Program__c,
                Academic_Year__c = Academic_Year__c,
                Applicant_PA__c = Applicant_PA__c,
                Degree__c = Degree__c,


                Lead_Owner__c = "0054L000001IJ70QAG",
                OwnerId = "0054L000001IJ70QAG",
                Semester__c = Semester__c,
                LastModifiedById = "0054L000001IJ70QAG",
                Stage__c = "Signed Up",
                Term_Settings__c = Term_Settings__c,
                University__c = University__c,
                Application_Source__c = "United Education",

                CreatedById = "0054L000001IJ70QAG",
                Student_Email__c = Student_Email__c
            


            };



            //update
            return await _uniService.Insert(sObject, body);
        }
        async Task<string> insertappliedprogram(string Price_List_Entry__c, string Application__c)
        {


            string sObject = "Applied_Programs__c";
            object body = new object();


            body = new
            {

                Price_List_Entry__c = Price_List_Entry__c,
                Application__c = Application__c




            };



            //update
            return await _uniService.Insert(sObject, body);
        }
        async Task<string> insertinvoice(string Amount__c, string Application__c, string Account__c, string Billing_To_Account__c, string Currency_Type__c)
        {


            string sObject = "Invoice__c";
            object body = new object();


            body = new
            {

                Amount__c = Amount__c,
                Application__c = Application__c,
                Account__c = Account__c,
                Billing_To_Account__c = Billing_To_Account__c,
                Currency_Type__c = Currency_Type__c,
                Invoice_Date__c = DateTime.Now.ToString("yyyy-MM-dd"),
                RecordTypeId = "0128d000000Z2uZAAS",
                Status__c = "Planned"
            };



            //update
            return await _uniService.Insert(sObject, body);
        }
        async Task<string> updateappinvoice(string Deposit_Invoice__c, string recordid)
        {


            string sObject = "Application__c";
            object body = new object();


            body = new
            {

                Deposit_Invoice__c = Deposit_Invoice__c,

            };



            //update
            return await _uniService.Update(sObject, recordid, body);
        }
        async Task<string> updateappstage(string recordid)
        {


            string sObject = "Application__c";
            object body = new object();


            body = new
            {

                Application_Form_Stage__c = "Completed",
                Stage__c = "Waiting Approval"

            };



            //update
            return await _uniService.Update(sObject, recordid, body);
        }
        async Task<string> updatecontact(string recordid)
        {


            string sObject = "Contact";
            object body = new object();


            body = new
            {

                Completed__c = true,


            };



            //update
            return await _uniService.Update(sObject, recordid, body);
        }
        private readonly long _maxFileSize = 20 * 1024 * 1024; // 20MB
        private readonly string[] _allowedMimeTypes = {
        "image/jpeg", "image/png", "image/gif", "image/webp", "application/pdf"
    };
        private readonly string[] _allowedExtensions = {
        ".jpg", ".jpeg", ".png", ".gif", ".webp", ".pdf"
    };
        [HttpPost("uploadunitedfile")]
        public async Task<IActionResult> UploadUnitedFile([FromForm] IFormFile file, [FromForm] string accountid, [FromForm] string appid, [FromForm] string filetype)
        {
            if (file == null || file.Length == 0)
                return BadRequest(new { msg = "No file uploaded" });

            if (file.Length > 20 * 1024 * 1024)
                return BadRequest(new { msg = "File exceeds 20MB" });

            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp", ".pdf" };
            var allowedMimeTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/webp", "application/pdf" };

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();

            if (!allowedExtensions.Contains(extension) || !allowedMimeTypes.Contains(file.ContentType.ToLowerInvariant()))
                return BadRequest(new { msg = "Unsupported file type" });

            using var ms = new MemoryStream();
            await file.CopyToAsync(ms);
            var fileBytes = ms.ToArray();

            if (!IsValidFileSignature(fileBytes))
                return BadRequest(new { msg = "Invalid file signature or unsafe file" });
            string contactid = PersonContactId(accountid);
            // احفظ الملف أو أرسله لأي مكان بدك
            // مثلاً:
            string base64 = Convert.ToBase64String(fileBytes);

            var responseFromServer = await uploadfile(filetype + extension, accountid, base64);
            JObject jsonObject = JObject.Parse(responseFromServer);

            string Idcontentversion = (string)jsonObject["id"];
            var responseFromServer1 = ContentDocument(Idcontentversion);
            string ContentDocumentid = responseFromServer1.ToString();

            JArray responseFromServer2 = RDA__cid(contactid, filetype);
            JObject firstRecord = (JObject)responseFromServer2[0];

            // Extract fields from the first record
            string rdaid = (string)firstRecord["Id"];

            string Content_Document_Id__c = (string)firstRecord["Content_Document_Id__c"];
            string Document_Name__c = (string)firstRecord["Document_Name__c"];
            string OwnerId = (string)firstRecord["OwnerId"];
            string RecordTypeId = (string)firstRecord["RecordTypeId"];
            bool Required__c = (bool)firstRecord["Required__c"];
            string Section_Name__c = (string)firstRecord["Section_Name__c"];
            int Sort_Order__c = (int)firstRecord["Sort_Order__c"];
            string Status__c = (string)firstRecord["Status__c"];
            if (appid != "1")
            {
                List<string> listy = applicationlistforupload(contactid);
                foreach (var item in listy)
                {
                    var responseFromServer3 = connecttorda(rdaid, ContentDocumentid, item);
                    var responseFromServer4 = ContentDocumentLink(ContentDocumentid, rdaid);
                    var responseFromServer5 = createrdaapplication(Content_Document_Id__c, item, Document_Name__c, Required__c, Section_Name__c, Sort_Order__c, Status__c);
                    var responseFromServer6 = ContentDocumentLinkapp(ContentDocumentid, item);

                }


            }
            else
            {
                var responseFromServer3 = connecttordacon(rdaid, ContentDocumentid, contactid);
                var responseFromServer4 = ContentDocumentLink(ContentDocumentid, rdaid);
                List<string> listy = applicationlistforupload(contactid);
                foreach (var item in listy)
                {

                    var responseFromServer31 = connecttorda(rdaid, ContentDocumentid, item);

                    var responseFromServer5 = createrdaapplication(Content_Document_Id__c, item, Document_Name__c, Required__c, Section_Name__c, Sort_Order__c, Status__c);
                    var responseFromServer6 = ContentDocumentLinkapp(ContentDocumentid, item);



                }
            }
            var tt = updateEDIS_Document_Created(accountid);

            ApiResponse res = new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = "File uploaded and validated successfully",
                Data = null
            };

            return Ok(res);


        }
        async Task<string> connecttordacon(string rdaid, string Content_Document_Id__c, string appid)
        {


            string sObject = "RDA__c";
            object body = new object();


            body = new
            {
                Status__c = "Uploaded",
                Content_Document_Id__c = Content_Document_Id__c,
                Uploaded_Date__c = DateTime.Now.ToString("yyyy-MM-dd"),
                Applicant_PA__c = appid


            };



            //update
            return await _uniService.Update(sObject, rdaid, body);
        }
        public class ApiResponse1
        {
            public int TotalSize { get; set; }
            public bool Done { get; set; }
            public List<Recordapplists> Records { get; set; }
        }
        public class Recordapplists
        {
            public Attributes Attributes { get; set; }
            public string Id { get; set; }
        }
        public List<string> applicationlistforupload(string termid)
        {
            //prepare the query
            string query = "SELECT Id  FROM Application__c WHERE Applicant_PA__c= '" + termid + "'";

            //Query
            var result = _uniService.Query(query);


            ApiResponse1 json1 = JsonConvert.DeserializeObject<ApiResponse1>(result.Result);
            List<string> ids = new List<string>();

            // Add each Id to the list
            if (json1?.Records != null)
            {
                foreach (var record in json1.Records)
                {
                    ids.Add(record.Id);
                }
            }
            return ids;
        }
        async Task<string> createrdaapplication(string ContentDocumentId, string appid, string Document_Name__c, bool Required__c, string Section_Name__c, int Sort_Order__c, string Status__c)
        {

            string sObject = "RDA__c";
            object body = new object();


            body = new
            {


                Application__c = appid,
                RecordId__c = appid, //Application Id


                Content_Document_Id__c = ContentDocumentId,
                Document_Name__c = Document_Name__c,
                OwnerId = "0058d000004DQjgAAG",
                RecordTypeId = "0128d000000Z2ubAAC",
                Required__c = Required__c,
                Section_Name__c = Section_Name__c,
                Sort_Order__c = Sort_Order__c,
                Status__c = Status__c,
                Uploaded_Date__c = DateTime.Now.ToString("yyyy-MM-dd"),




            };



            //update
            return await _uniService.Insert(sObject, body);
        }

        private bool IsValidFileSignature(byte[] fileBytes)
        {
            if (fileBytes.Length < 4)
                return false;

            // PDF
            if (fileBytes[0] == 0x25 && fileBytes[1] == 0x50 && fileBytes[2] == 0x44 && fileBytes[3] == 0x46)
                return true;

            // JPEG
            if (fileBytes[0] == 0xFF && fileBytes[1] == 0xD8 && fileBytes[2] == 0xFF)
                return true;

            // PNG
            if (fileBytes[0] == 0x89 && fileBytes[1] == 0x50 && fileBytes[2] == 0x4E && fileBytes[3] == 0x47)
                return true;

            // GIF
            if (fileBytes[0] == 0x47 && fileBytes[1] == 0x49 && fileBytes[2] == 0x46 && fileBytes[3] == 0x38)
                return true;

            // WebP
            if (fileBytes.Length >= 12 &&
                fileBytes[0] == 0x52 && fileBytes[1] == 0x49 && fileBytes[2] == 0x46 && fileBytes[3] == 0x46 &&
                fileBytes[8] == 0x57 && fileBytes[9] == 0x45 && fileBytes[10] == 0x42 && fileBytes[11] == 0x50)
                return true;

            return false;
        }
        async Task<string> ContentDocumentLink(string ContentDocumentId, string rdaid)
        {

            string sObject = "ContentDocumentLink";
            object body = new object();


            body = new
            {


                ContentDocumentId = ContentDocumentId,
                LinkedEntityId = rdaid,

                ShareType = "V",
                Visibility = "AllUsers",




            };



            //update
            return await _uniService.Insert(sObject, body);
        }

        async Task<string> updateEDIS_Document_Created(string rdaid)
        {


            string sObject = "Account";
            object body = new object();


            body = new
            {
                EDIS_Document_Created__pc = true


            };



            //update
            return await _uniService.Update(sObject, rdaid, body);
        }
        async Task<string> ContentDocumentLinkapp(string ContentDocumentId, string appid)
        {

            string sObject = "ContentDocumentLink";
            object body = new object();


            body = new
            {


                ContentDocumentId = ContentDocumentId,
                LinkedEntityId = appid,

                ShareType = "V",
                Visibility = "InternalUsers",




            };



            //update
            return await _uniService.Insert(sObject, body);
        }
        async Task<string> connecttorda(string rdaid, string Content_Document_Id__c, string appid)
        {


            string sObject = "RDA__c";
            object body = new object();


            body = new
            {
                Status__c = "Uploaded",
                Content_Document_Id__c = Content_Document_Id__c,
                Uploaded_Date__c = DateTime.Now.ToString("yyyy-MM-dd"),
                Applicant_PA__c = appid


            };



            //update
            return await _uniService.Update(sObject, rdaid, body);
        }
        public JArray RDA__cid(string leadId, string type)
        {

            //prepare the query
            string query = "SELECT Id,Content_Document_Id__c,Document_Name__c,OwnerId,RecordTypeId,Required__c,Section_Name__c,Sort_Order__c,Status__c,Uploaded_Date__c    FROM RDA__c   WHERE  Applicant_PA__c = '" + leadId + "' and  Document_Name__c  = '" + type + "'";

            //Query
            var result = _uniService.Query(query);

            JObject json = JObject.Parse(result.Result);
            JArray records = (JArray)json["records"];

            return records;
        }
        async Task<string> uploadfile(string Title, string contactid, string base64file)
        {

            string sObject = "ContentVersion";
            object body = new object();


            body = new
            {


                Title = Title,
                PathOnClient = Title,

                ContentLocation = "S",
                FirstPublishLocationId = contactid,
                VersionData = base64file



            };



            //update
            return await _uniService.Insert(sObject, body);
        }

        public string ContentDocument(string leadId)
        {

            //prepare the query
            string query = "SELECT ContentDocumentId    FROM ContentVersion  WHERE  Id= '" + leadId + "'";

            //Query
            var result = _uniService.Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["ContentDocumentId"];

            return value;
        }
        public string ContentDistributionurl(string leadId)
        {

            //prepare the query
            string query = "SELECT DistributionPublicUrl FROM ContentDistribution   WHERE  Id= '" + leadId + "'";

            //Query
            var result = _uniService.Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["DistributionPublicUrl"];

            return value;
        }
        private string DetermineMimeType(string base64Data)
        {
            // Ensure base64Data is properly trimmed and clean
            base64Data = base64Data.Replace("\"", "").Trim();

            // JPEG Image (base64 encoded data starts with /9j/ or /9j/4AAQSkZJRgABAQEAAAAAAAD)
            if (base64Data.StartsWith("/9j/") || base64Data.StartsWith("/9j/4AAQSkZJRgABAQEAAAAAAAD"))
                return "image/jpeg";

            // PNG Image (base64 encoded data starts with iVBORw0KGgo)
            if (base64Data.StartsWith("iVBORw0KGgo"))
                return "image/png";

            // GIF Image (base64 encoded data starts with R0lGOD)
            if (base64Data.StartsWith("R0lGOD"))
                return "image/gif";

            // SVG Image (base64 encoded data starts with PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmci)
            if (base64Data.StartsWith("PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmci"))
                return "image/svg+xml";

            // PDF Document (base64 encoded data starts with JVBERi)
            if (base64Data.StartsWith("JVBERi"))
                return "application/pdf";

            // Microsoft Word Document (docx)
            if (base64Data.StartsWith("UEsDBBQAAAAI"))
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

            // Microsoft Excel Document (xlsx)
            if (base64Data.StartsWith("UEsDBBQAAAAI"))
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            // Microsoft PowerPoint Presentation (pptx)
            if (base64Data.StartsWith("UEsDBBQAAAAI"))
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";

            // Text File
            if (base64Data.StartsWith("dGV4dC"))
                return "text/plain";

            // CSV File
            if (base64Data.StartsWith("Y29sdW1uLGRhdGEsIG9ic2VydmF0aW9uLCBsaW5lIHRleHQgU29ycnkgRGVsaW5lc3Q="))
                return "text/csv";

            // If not recognized, return fallback
            return "application/octet-stream"; // Fallback for unknown types
        }


        [HttpGet("GetBase64Data")]
        public async Task<ActionResult> GetBase64Data(string contentDocumentId)
        {

            string base64Data = await _uniService.countappAsync(contentDocumentId);

            // Determine MIME type based on Base64 data
            string mimeType = DetermineMimeType(base64Data);

            // Return both the MIME type and Base64 data as JSON
            var result = new { MimeType = mimeType, Base64Data = base64Data.Replace("\"", "") };

            return Ok(new
            {
                MimeType = mimeType,
                Base64Data = base64Data.Replace("\"", "")
            });


        }
        public class Rootrdalist
        {
            public int totalSize { get; set; }
            public bool done { get; set; }
            public List<Recordrdalist> records { get; set; }
        }
        public class Recordrdalist
        {
            public Attributes attributes { get; set; }
            public string Document_Name__c { get; set; }
            public string Status__c { get; set; }
            public string Section_Name__c { get; set; }
            public string Content_Document_Id__c { get; set; }
            public string Id { get; set; }

        }
        public async Task<Rootrdalist> rdalistapp(string termid)
        {
            //prepare the query
            string query = "SELECT Document_Name__c,Status__c,Section_Name__c,Content_Document_Id__c,Id FROM RDA__c WHERE Applicant_PA__c='" + termid + "'";

            //Query
            var result = await _uniService.Query(query);

            JObject json = JObject.Parse(result);
            Rootrdalist json1 = JsonConvert.DeserializeObject<Rootrdalist>(result);

            return json1;
        }

        [HttpGet("documents")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<object>> GetDocumentListAsync()
        {
            try
            {
                var studentId = await GetCurrentStudentId();
                if (studentId == null)
                {
                    return Unauthorized(new ApiResponse

                    {
                        Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", "en"),
                    Language = "en"
                });
                }
                string accountid = await _context.Students
                    .Where(s => s.Id == studentId)
                    .Select(s => s.CrmId)
                    .FirstOrDefaultAsync();
                string contactid = PersonContactId(accountid);
                Rootrdalist json2 = await rdalistapp(contactid);
                return Ok(new 
                { 
                    success = true, 
                    data = json2.records
                  
                });
            }
            catch (Exception ex)
            {
            
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new { message = "An error occurred while fetching documents" });
            }
        }
    }
}
