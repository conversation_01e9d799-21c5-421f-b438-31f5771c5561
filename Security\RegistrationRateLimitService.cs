using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;

namespace StudentManagementAPI.Security
{
    // Specialized rate limiting service for registration endpoint
    public class RegistrationRateLimitService : IRateLimitService
    {
        private readonly Dictionary<string, Queue<DateTime>> _requestStore = new Dictionary<string, Queue<DateTime>>();
        private readonly object _lock = new object();
        
        // Maximum number of requests allowed in the time window (1000 per second)
        private const int MaxRequests = 1000;
        
        // Time window in seconds (1 second)
        private const int TimeWindowSeconds = 1;

        public Task<bool> IsRateLimitedAsync(string clientId)
        {
            CleanupExpiredRequests(clientId);
            
            lock (_lock)
            {
                if (!_requestStore.ContainsKey(clientId))
                {
                    return Task.FromResult(false);
                }

                return Task.FromResult(_requestStore[clientId].Count >= MaxRequests);
            }
        }

        public Task RecordRequestAsync(string clientId)
        {
            lock (_lock)
            {
                if (!_requestStore.ContainsKey(clientId))
                {
                    _requestStore[clientId] = new Queue<DateTime>();
                }

                _requestStore[clientId].Enqueue(DateTime.UtcNow);
            }

            return Task.CompletedTask;
        }

        private void CleanupExpiredRequests(string clientId)
        {
            lock (_lock)
            {
                if (!_requestStore.ContainsKey(clientId))
                {
                    return;
                }

                var queue = _requestStore[clientId];
                var cutoffTime = DateTime.UtcNow.AddSeconds(-TimeWindowSeconds);

                while (queue.Count > 0 && queue.Peek() < cutoffTime)
                {
                    queue.Dequeue();
                }

                if (queue.Count == 0)
                {
                    _requestStore.Remove(clientId);
                }
            }
        }
    }

    // Registration-specific rate limiting middleware
    public class RegistrationRateLimitMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly RegistrationRateLimitService _rateLimitService;

        public RegistrationRateLimitMiddleware(RequestDelegate next, RegistrationRateLimitService rateLimitService)
        {
            _next = next;
            _rateLimitService = rateLimitService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only apply rate limiting to the registration endpoint
            if (IsRegistrationEndpoint(context))
            {
                // Get client identifier (IP address or API key if available)
                string clientId = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
                
                // Check if client has exceeded rate limit
                if (await _rateLimitService.IsRateLimitedAsync(clientId))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                    context.Response.Headers.Append("Retry-After", TimeSpan.FromSeconds(1).TotalSeconds.ToString());
                    await context.Response.WriteAsync("Registration rate limit exceeded. Please try again later.");
                    return;
                }

                // Record this request
                await _rateLimitService.RecordRequestAsync(clientId);
            }

            // Continue processing the request
            await _next(context);
        }

        private bool IsRegistrationEndpoint(HttpContext context)
        {
            return context.Request.Method == "POST" && 
                   context.Request.Path.Value != null && 
                   context.Request.Path.Value.EndsWith("/api/auth/register", StringComparison.OrdinalIgnoreCase);
        }
    }
}