﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;

namespace StudentManagementAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FileUploadController : ControllerBase
    {
        private readonly IWebHostEnvironment _env;
        private readonly ILogger<FileUploadController> _logger;

        public FileUploadController(IWebHostEnvironment env, ILogger<FileUploadController> logger)
        {
            _env = env ?? throw new ArgumentNullException(nameof(env));
            _logger = logger;
        }

        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(IFormFile file)
        {
            if (file == null || file.Length == 0)
                return BadRequest("No file uploaded.");

            // Define allowed image extensions
            var allowedExtensions = new HashSet<string> { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

            if (!allowedExtensions.Contains(fileExtension))
                return BadRequest("Only image files are allowed.");

            // Check file size (e.g., max 5MB)
            const long maxFileSize = 5 * 1424 * 1024; // 5 MB
            if (file.Length > maxFileSize)
                return BadRequest("File size exceeds the limit of 5MB.");

            // Validate MIME type
            if (!IsValidMimeType(file.ContentType))
                return BadRequest("Invalid file MIME type.");

            // Validate file signature
            if (!await IsValidImageSignature(file))
                return BadRequest("Invalid image file.");

            var webRootPath = _env.WebRootPath;
            _logger.LogInformation($"WebRootPath: {webRootPath}");

            if (string.IsNullOrEmpty(webRootPath))
            {
                return StatusCode(500, "WebRootPath is not configured.");
            }

            var uploadsFolder = Path.Combine(webRootPath, "uploads");
            if (!Directory.Exists(uploadsFolder))
            {
                Directory.CreateDirectory(uploadsFolder);
            }

            // Sanitize file name
            var sanitizedFileName = SanitizeFileName(file.FileName);
            var uniqueFileName = GenerateUniqueFileName(sanitizedFileName);
            var filePath = Path.Combine(uploadsFolder, uniqueFileName);

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await file.CopyToAsync(stream);
            }

            var fileUrl = $"{Request.Scheme}://{Request.Host}/uploads/{uniqueFileName}";
            return Ok(new { FileUrl = fileUrl });
        }

        private bool IsValidMimeType(string contentType)
        {
            // Define allowed MIME types
            var allowedMimeTypes = new HashSet<string>
            {
                MediaTypeNames.Image.Jpeg,
                MediaTypeNames.Image.Png,
                MediaTypeNames.Image.Gif,
                MediaTypeNames.Image.Bmp
            };

            return allowedMimeTypes.Contains(contentType);
        }

        private async Task<bool> IsValidImageSignature(IFormFile file)
        {
            // Define image signatures
            var imageSignatures = new Dictionary<string, byte[]>
            {
                { ".jpg", new byte[] { 0xFF, 0xD8, 0xFF } },
                { ".jpeg", new byte[] { 0xFF, 0xD8, 0xFF } },
                { ".png", new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A } },
                { ".gif", new byte[] { 0x47, 0x49, 0x46, 0x38 } },
                { ".bmp", new byte[] { 0x42, 0x4D } }
            };

            if (!imageSignatures.TryGetValue(Path.GetExtension(file.FileName).ToLowerInvariant(), out var signature))
            {
                return false;
            }

            using (var memoryStream = new MemoryStream())
            {
                await file.CopyToAsync(memoryStream);
                memoryStream.Seek(0, SeekOrigin.Begin);

                var buffer = new byte[signature.Length];
                await memoryStream.ReadAsync(buffer, 0, buffer.Length);

                return buffer.SequenceEqual(signature);
            }
        }

        private string SanitizeFileName(string fileName)
        {
            // Remove invalid characters and sanitize the file name
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitizedFileName = new string(fileName.Where(c => !invalidChars.Contains(c)).ToArray());
            return sanitizedFileName;
        }

        private string GenerateUniqueFileName(string fileName)
        {
            // Generate a unique file name using a hash
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(fileName + DateTime.UtcNow.ToString()));
                var hashString = Convert.ToHexString(hashBytes).ToLowerInvariant();
                var fileExtension = Path.GetExtension(fileName);
                return $"{hashString}{fileExtension}";
            }
        }
    }
}
