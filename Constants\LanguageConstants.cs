public static class LanguageConstants
{
    public static readonly string[] SupportedLanguages = new[] { "en", "ar", "ru", "fa" };
    public const string DefaultLanguage = "en";

    public static readonly Dictionary<string, Dictionary<string, string>> Messages = new()
    {
        ["en"] = new Dictionary<string, string>
        {
            ["Unauthorized"] = "Unauthorized access",
            ["NotFound"] = "Resource not found",
            ["TestNotFound"] = "Test attempt not found",
            ["TestCompleted"] = "Test has been completed",
            ["InvalidLanguage"] = "Invalid language specified",
            ["Success"] = "Operation completed successfully",
            ["EmailExists"] = "A student with this email address already exists in the system",
            ["PhoneExists"] = "A student with this phone number already exists in the system",
            ["StudentNotFound"] = "Student not found",
            ["QuestionNotFound"] = "Question not found",
            ["CompletedTestError"] = "Cannot update answers for a completed test",
            ["AnswerUpdated"] = "Answer updated successfully",
            ["NoActiveQuestions"] = "No active questions available for the test",
            ["InvalidRequest"] = "Invalid request",
            ["UserNotStudent"] = "User is not associated with a student account",
            ["ValidationError"] = "Validation error occurred",
            ["StudentUpdated"] = "Student information updated successfully",
            ["InternalServerError"] = "An internal server error occurred. Please try again later.",
            ["ImageUpdated"] = "Profile image updated successfully",
            ["NoFileUploaded"] = "No file was uploaded",
            ["InvalidFileType"] = "Invalid file type. Only JPG, JPEG, PNG, and GIF are allowed."
        },
        ["ar"] = new Dictionary<string, string>
        {
            ["Unauthorized"] = "غير مصرح",
            ["NotFound"] = "لم يتم العثور على المورد",
            ["TestNotFound"] = "لم يتم العثور على محاولة الاختبار",
            ["TestCompleted"] = "تم إكمال الاختبار",
            ["InvalidLanguage"] = "لغة غير صالحة محددة",
            ["Success"] = "تمت العملية بنجاح",
            ["EmailExists"] = "يوجد طالب مسجل بهذا البريد الإلكتروني",
            ["PhoneExists"] = "يوجد طالب مسجل بهذا الرقم",
            ["StudentNotFound"] = "لم يتم العثور على الطالب",
            ["QuestionNotFound"] = "لم يتم العثور على السؤال",
            ["CompletedTestError"] = "لا يمكن تحديث الإجابات للاختبار المكتمل",
            ["AnswerUpdated"] = "تم تحديث الإجابة بنجاح",
            ["NoActiveQuestions"] = "لا توجد أسئلة نشطة متاحة للاختبار",
            ["InvalidRequest"] = "طلب غير صالح",
            ["UserNotStudent"] = "المستخدم غير مرتبط بحساب طالب",
            ["ValidationError"] = "حدث خطأ في التحقق من الصحة",
            ["StudentUpdated"] = "تم تحديث معلومات الطالب بنجاح",
            ["InternalServerError"] = "حدث خطأ في الخادم الداخلي. يرجى المحاولة مرة أخرى لاحقاً.",
            ["ImageUpdated"] = "تم تحديث صورة الملف الشخصي بنجاح",
            ["NoFileUploaded"] = "لم يتم تحميل أي ملف",
            ["InvalidFileType"] = "نوع الملف غير صالح. يُسمح فقط بملفات JPG وJPEG وPNG وGIF."
        },
        ["ru"] = new Dictionary<string, string>
        {
            ["Unauthorized"] = "Неавторизованный доступ",
            ["NotFound"] = "Ресурс не найден",
            ["TestNotFound"] = "Попытка теста не найдена",
            ["TestCompleted"] = "Тест завершен",
            ["InvalidLanguage"] = "Указан неверный язык",
            ["Success"] = "Операция успешно завершена",
            ["EmailExists"] = "Студент с таким email уже существует в системе",
            ["PhoneExists"] = "Студент с таким номером телефона уже существует в системе",
            ["StudentNotFound"] = "Студент не найден",
            ["QuestionNotFound"] = "Вопрос не найден",
            ["CompletedTestError"] = "Невозможно обновить ответы для завершенного теста",
            ["AnswerUpdated"] = "Ответ успешно обновлен",
            ["NoActiveQuestions"] = "Нет активных вопросов для теста",
            ["InvalidRequest"] = "Неверный запрос",
            ["UserNotStudent"] = "Пользователь не связан с учетной записью студента",
            ["ValidationError"] = "Произошла ошибка проверки",
            ["StudentUpdated"] = "Информация о студенте успешно обновлена",
            ["InternalServerError"] = "Произошла внутренняя ошибка сервера. Пожалуйста, попробуйте позже.",
            ["ImageUpdated"] = "Изображение профиля успешно обновлено",
            ["NoFileUploaded"] = "Файл не был загружен",
            ["InvalidFileType"] = "Недопустимый тип файла. Разрешены только JPG, JPEG, PNG и GIF."
        },
        ["fa"] = new Dictionary<string, string>
        {
            ["Unauthorized"] = "دسترسی غیرمجاز",
            ["NotFound"] = "منبع یافت نشد",
            ["TestNotFound"] = "تلاش آزمون یافت نشد",
            ["TestCompleted"] = "آزمون تکمیل شده است",
            ["InvalidLanguage"] = "زبان نامعتبر مشخص شده است",
            ["Success"] = "عملیات با موفقیت انجام شد",
            ["EmailExists"] = "دانشجویی با این آدرس ایمیل در سیستم وجود دارد",
            ["PhoneExists"] = "دانشجویی با این شماره تلفن در سیستم وجود دارد",
            ["StudentNotFound"] = "دانشجو یافت نشد",
            ["QuestionNotFound"] = "سؤال یافت نشد",
            ["CompletedTestError"] = "نمی‌توان پاسخ‌های آزمون تکمیل شده را به‌روز کرد",
            ["AnswerUpdated"] = "پاسخ با موفقیت به‌روز شد",
            ["NoActiveQuestions"] = "هیچ سؤال فعالی برای آزمون موجود نیست",
            ["InvalidRequest"] = "درخواست نامعتبر",
            ["UserNotStudent"] = "کاربر با حساب دانشجویی مرتبط نیست",
            ["ValidationError"] = "خطای اعتبارسنجی رخ داده است",
            ["StudentUpdated"] = "اطلاعات دانشجو با موفقیت به‌روزرسانی شد",
            ["InternalServerError"] = "خطای داخلی سرور رخ داده است. لطفاً بعداً دوباره تلاش کنید.",
            ["ImageUpdated"] = "تصویر پروفایل با موفقیت به‌روزرسانی شد",
            ["NoFileUploaded"] = "هیچ فایلی آپلود نشده است",
            ["InvalidFileType"] = "نوع فایل نامعتبر است. فقط فایل‌های JPG، JPEG، PNG و GIF مجاز هستند."
        }
    };
}