using System.Collections.Generic;
using Newtonsoft.Json;

namespace StudentManagementAPI.Models.Responses
{
    public class ApplicationResponse
    {
        [JsonProperty("Id")]
        public string Id { get; set; }

        [JsonProperty("Name")]
        public string Name { get; set; }

        [JsonProperty("Applicant_Name__c")]
        public string ApplicantName { get; set; }

        [JsonProperty("Mobile__c")]
        public string Mobile { get; set; }

        [<PERSON>son<PERSON>roperty("Student_Email__c")]
        public string StudentEmail { get; set; }

        [JsonProperty("Stage__c")]
        public string Stage { get; set; }

        [JsonProperty("University_Name__c")]
        public string UniversityName { get; set; }

        [JsonProperty("Program_Name__c")]
        public string ProgramName { get; set; }

        [JsonProperty("Academic_Year__c")]
        public string AcademicYear { get; set; }

        [JsonProperty("Semester__c")]
        public string Semester { get; set; }

        [Json<PERSON>roperty("University_PIN_Code__c")]
        public string UniversityPinCode { get; set; }

        [JsonProperty("Offer_Letter_Link__c")]
        public string OfferLetterLink { get; set; }

        [JsonProperty("Acceptance_Letter_URL__c")]
        public string AcceptanceLetterUrl { get; set; }

        [JsonProperty("Scholarship__c")]
        public string Scholarship { get; set; }

        [JsonProperty("Is_Scholarship_Exist__c")]
        public bool? IsScholarshipExist { get; set; }

        [JsonProperty("Sub_Agency_Id__c")]
        public string SubAgencyId { get; set; }

        [JsonProperty("ApplicationStatus__c")]
        public string ApplicationStatus { get; set; }
    }

    public class ApplicationListResponse
    {
        [JsonProperty("totalSize")]
        public int TotalCount { get; set; }

        [JsonProperty("records")]
        public List<ApplicationResponse> Applications { get; set; } = new List<ApplicationResponse>();
    }
}
