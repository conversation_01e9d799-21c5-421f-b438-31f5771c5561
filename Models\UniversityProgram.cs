﻿namespace StudentManagementAPI.Models
{
    public class UniversityProgram





    {
        public string? Alternative_Program_Name__c { get; set; }
        public string? Campus__c { get; set; }
        public decimal? Cash_Payment_Fee__c { get; set; }
        public string? CurrencyType__c { get; set; }
        public decimal? Deposit_Price__c { get; set; }
        public string? Language__c { get; set; }
        public decimal? Prep_School_Fee__c { get; set; }
        public string? Program__c { get; set; }
        public string? Program_Degree__c { get; set; }
        public string? Program_Name__c { get; set; }
        public string? Semester__c { get; set; }
        public decimal? Tuition_Fee__c { get; set; }
        public string? University_Id__c { get; set; }
        public string? University_Name__c { get; set; }
        public string? University_Account_Billing_Country__c { get; set; }
        public string? ProgramFa { get; set; }
        public string? ProgramRu { get; set; }
        public string? University_NameAr { get; set; }
        public string? University_NameRu { get; set; }
        public string? University_NameFa { get; set; }
        public string? University_Logo__c { get; set; }
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Academic_Year__c { get; set; }
     
        public string? CreatedById { get; set; }
     
        public string? Discipline__c { get; set; }
        public decimal? Discounted_Tuition_Fee__c { get; set; }
        public string? Duration__c { get; set; }
        public string? Faculty_Institute__c { get; set; }

        public string? LastModifiedById { get; set; }
        public string? Old_Id__c { get; set; }
        public string OwnerId { get; set; }
        public bool? Passive__c { get; set; }
   
        public string Name { get; set; }
   
        public bool? Quota_Full__c { get; set; }
        public int? Quota_of_Program__c { get; set; }

        public bool? Term_is_Active__c { get; set; }
        public string Term_Settings__c { get; set; }
    
        public string University_Billing_Country__c { get; set; }
        public string? University_City__c { get; set; }
        public double? University_Grade__c { get; set; }

        public int? University_Maximum_Selection__c { get; set; }

        public string? with_Thesis_non_Thesis__c { get; set; }
        public string? Year__c { get; set; }
      
    }
}
