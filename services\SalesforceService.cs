using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static StudentManagementAPI.Controllers.SalesforceUsersController;

namespace StudentManagementAPI.Services
{
    public class SalesforceService : ISalesforceService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SalesforceService> _logger;
        private string _accessToken;
        private DateTime _tokenExpiry;

        public SalesforceService(
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            ILogger<SalesforceService> logger)
        {

            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<string> GetAccessTokenAsync()
        {
            if (!string.IsNullOrEmpty(_accessToken) && DateTime.UtcNow < _tokenExpiry)
            {
                return _accessToken;
            }

            var client = _httpClientFactory.CreateClient();
            var request = new HttpRequestMessage(System.Net.Http.HttpMethod.Post, _configuration["Salesforce:TokenEndpoint"])
            {
                Content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", "password"),
                    new KeyValuePair<string, string>("client_id", _configuration["Salesforce:ClientId"]),
                    new KeyValuePair<string, string>("client_secret", _configuration["Salesforce:ClientSecret"]),
                    new KeyValuePair<string, string>("username", _configuration["Salesforce:Username"]),
                    new KeyValuePair<string, string>("password", _configuration["Salesforce:Password"])
                })
            };

            try
            {
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                var content = await response.Content.ReadAsStringAsync();
                using var doc = JsonDocument.Parse(content);
                _accessToken = doc.RootElement.GetProperty("access_token").GetString();
                var expiresIn = doc.RootElement.GetProperty("expires_in").GetInt32();
                _tokenExpiry = DateTime.UtcNow.AddSeconds(expiresIn - 300); // 5 minutes buffer
                
                return _accessToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Salesforce access token");
                throw new ApplicationException("Failed to authenticate with Salesforce", ex);
            }
        }

        public async Task<string> QueryAsync(string soqlQuery)
        {
            var token = await GetAccessTokenAsync();
            var client = _httpClientFactory.CreateClient();
            var instanceUrl = _configuration["Salesforce:InstanceUrl"] ?? "https://vaha.m_uniService.salesforce.com";
            var request = new HttpRequestMessage(
System.Net.Http.HttpMethod.Get,
                $"{instanceUrl}/services/data/v57.0/query?q={Uri.EscapeDataString(soqlQuery)}");

            request.Headers.Add("Authorization", $"Bearer {token}");

            try
            {
                var response = await client.SendAsync(request);
                response.EnsureSuccessStatusCode();
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing Salesforce query: {Query}", soqlQuery);
                throw new ApplicationException("Failed to execute Salesforce query", ex);
            }
        }
    }
}
