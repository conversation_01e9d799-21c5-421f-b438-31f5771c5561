using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Linq;
using System.Text;

namespace StudentManagementAPI.Models
{
    public class Question
    {
        [Key]
        public int Id { get; set; }
        [Required]
        public string TextEn { get; set; }

        [Required]
        public string TextAr { get; set; }
        [Required]
        public string TextRu { get; set; }
        [Required]
        public string TextFa { get; set; }

        [NotMapped]
        public string Text 
        { 
            get 
            {
                // Default to English if no language is specified
                return TextEn;
            }
            set
            {
                // When setting Text, update TextEn by default
                TextEn = value;
            }
        }

        public int PersonalityTypeId { get; set; }
        public virtual PersonalityTypes  PersonalityTypes { get; set; }

        public int OrderNumber { get; set; }
        public bool IsActive { get; set; }

        public virtual ICollection<Answer> Answers { get; set; }
    }
}