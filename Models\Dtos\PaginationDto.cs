namespace StudentManagementAPI.Models.Dtos
{
    public class PaginationDto
    {
        private const int DefaultPage = 1;
        private const int DefaultPageSize = 20;
        private const int MaxPageSize = 100;

        private int _page = DefaultPage;
        private int _pageSize = DefaultPageSize;

        public int Page
        {
            get => _page;
            set => _page = (value < 1) ? DefaultPage : value;
        }

        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value < 1 || value > MaxPageSize) ? DefaultPageSize : value;
        }

        public string SortColumn { get; set; } = "CreatedDate";
        public string SortDirection { get; set; } = "DESC";

        public int Skip => (Page - 1) * PageSize;
        public int Take => PageSize;
    }
}
