﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StudentManagementAPI.Configuration;
using StudentManagementAPI.Controllers;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using System.Security.Claims;
using static StudentManagementAPI.Controllers.ApplicationController;

[ApiController]
[Route("api/[controller]")]
public class externalregController : ControllerBase
{
    private readonly UniversityProgramsDbContext _contextuni;
    private readonly UniversityProgramsDbContext _context;
  
    private readonly UserManager<ApplicationUser> _userManager;
    private readonly ILanguageService _languageService;
    private readonly RsaHelper _uniService1;
    private readonly ILogger<ApplicationController> _logger;
    private readonly IOptions<SalesforceSettings> _salesforceSettings;
    private readonly string[] _supportedLanguages = new[] { "en", "ar", "ru", "fa" };
    private const string DEFAULT_LANGUAGE = "en";

    public externalregController(
        UniversityProgramsDbContext context,
           UniversityProgramsDbContext contextuni,
        UserManager<ApplicationUser> userManager,
        ILanguageService languageService,
        getuni2023 uniService,
        RsaHelper uniService1,
        ILogger<ApplicationController> logger,
        IOptions<SalesforceSettings> salesforceSettings)
    {
        _contextuni = contextuni;
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        _languageService = languageService ?? throw new ArgumentNullException(nameof(languageService));
        _uniService = uniService;
        _uniService1 = uniService1;
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _salesforceSettings = salesforceSettings ?? throw new ArgumentNullException(nameof(salesforceSettings));
    }
    private readonly getuni2023 _uniService;
    private string ValidateLanguage(string lang)
    {
        if (string.IsNullOrEmpty(lang) || !_supportedLanguages.Contains(lang.ToLower()))
        {
            return DEFAULT_LANGUAGE;
        }
        return lang.ToLower();
    }
    public class Rootlistd
    {
        public int totalSize { get; set; }
        public bool done { get; set; }
        public string nextRecordsUrl { get; set; }
        public List<Recordd> records { get; set; }
    }

    public class Recordd
    {
        public string Id { get; set; }
        public string Term_Name_Formula__c { get; set; }
        public string Name { get; set; }
        public string Semester__c { get; set; }
        public string Academic_Year__c { get; set; }
        public string Term_Logo__c { get; set; }
        public string Degree__c { get; set; }

        public string Term_Settings__c { get; set; }
    }
    [HttpGet("Termlist")]
    public async Task<ActionResult<ApiResponse>> Termlist([FromQuery] string lang = DEFAULT_LANGUAGE)
    {
        string language = !string.IsNullOrEmpty(lang) ? lang : lang;
        language = ValidateLanguage(language);


        //prepare the query
        // Query your local database using Entity Framework
        var records = await _contextuni.TermSetting.Where(a => a.Term_Settings == null).ToListAsync();

        // Map to API format if needed (e.g., match the structure of Rootlist1)
        var response = new Rootlist1
        {
            totalSize = records.Count,
            done = true, // You can modify this if you paginate
            records = records.Select(r => new Recordp
            {
                Id = r.Id,
                Term_Name_Formula__c = r.Term_Name_Formula,
                Semester__c = r.Semester,
                Academic_Year__c = r.Academic_Year
            }).ToList()
        };

        // Return the result
        return Ok(new ApiResponse
        {
            Status = true,
            Code = 200,
            Message = "",
            Data = response,
            Language = language
        });

    }
    [HttpGet("Degreelist")]
    public async Task<ActionResult<ApiResponse>> Degreelist([FromQuery] string Termid, string lang = DEFAULT_LANGUAGE)
    {
        string language = !string.IsNullOrEmpty(lang) ? lang : lang;
        language = ValidateLanguage(language);

 

        var records = await _contextuni.TermSetting
  .Where(x => x.Term_Settings == Termid)
  .ToListAsync();


        // Map to API format if needed (e.g., match the structure of Rootlist1)
        var result = new Rootlistd
        {
            totalSize = records.Count,
            done = true,
            records = records.Select(r => new Recordd
            {
                Id = r.Id,
                Term_Name_Formula__c = r.Term_Name_Formula,
                Name = r.Name,
                Semester__c = r.Semester,
                Academic_Year__c = r.Academic_Year,
                Term_Logo__c = r.Term_Logo,
                Degree__c = r.Degree
            }).ToList()
        };


        // Return the result
        return Ok(new ApiResponse
        {
            Status = true,
            Code = 200,
            Message = "",
            Data = result,
            Language = language
        });
    }

    [HttpGet("Majorslist")]
    public async Task<ActionResult<ApiResponse>> Majorslist(
     [FromQuery] string Termid,
     string Degree, string? destination,
     string lang = DEFAULT_LANGUAGE)
    {
        var resultList = await _contextuni.UniversityProgram
.Where(x =>
    x.Term_Settings__c == Termid &&
    x.Program_Degree__c == Degree &&
    x.Term_is_Active__c == true &&
    x.Passive__c == false &&
    (string.IsNullOrEmpty(destination) || x.University_Account_Billing_Country__c == destination)
)
.Select(e => new
{
    e.Id,
    e.Academic_Year__c,
    Alternative_Program_Name__c = e.Alternative_Program_Name__c ?? "",
    Campus__c = e.Campus__c ?? "",
    Cash_Payment_Fee__c = e.Cash_Payment_Fee__c ?? 0,
    CurrencyType__c = e.CurrencyType__c ?? "",
    Deposit_Price__c = e.Deposit_Price__c ?? 0,
    Language__c = e.Language__c ?? "",
    Prep_School_Fee__c = e.Prep_School_Fee__c ?? 0,
    Program__c = e.Program__c ?? "",
    Program_Degree__c = e.Program_Degree__c ?? "",
    Program_Name__c = e.Program_Name__c ?? "",
    Semester__c = e.Semester__c ?? "",
    Tuition_Fee__c = e.Tuition_Fee__c ?? 0,
    University_Id__c = e.University_Id__c ?? "",
    University_Name__c = e.University_Name__c ?? "",
    University_Account_Billing_Country__c = e.University_Account_Billing_Country__c ?? "",
    ProgramFa = e.ProgramFa ?? "",
    ProgramRu = e.ProgramRu ?? "",
    University_NameAr = e.University_NameAr ?? "",
    University_NameRu = e.University_NameRu ?? "",
    University_NameFa = e.University_NameFa ?? "",
    Unilogo = string.IsNullOrEmpty(e.University_Logo__c) ? "NULL" : e.University_Logo__c
})
.ToListAsync();



        // إنشاء الرد النهائي
        var response = new
        {
            totalSize = resultList.Count,
            records = resultList
        };

        return Ok(new ApiResponse
        {
            Status = true,
            Code = 200,
            Message = "",
            Data = response,
            Language = lang
        });
    }

    // دالة تحويل إلى Decimal?
    private decimal? ParseDecimal(string value)
    {
        return decimal.TryParse(value, out var result) ? result : (decimal?)null;
    }


}
