﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

[Table("MajorClassifications")]
public class MajorClassification
{
    [Key]
    public int MajorID { get; set; }

    [MaxLength(500)]
    public string Program_EN { get; set; }

    [MaxLength(500)]
    public string Program_AR { get; set; }

    [MaxLength(500)]
    public string Program_RU { get; set; }

    [MaxLength(500)]
    public string Program_FA { get; set; }

    [MaxLength(100)]
    public string HollandCode { get; set; }

    public string Reasoning_EN { get; set; }
}
