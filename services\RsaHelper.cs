﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Security.Cryptography;
using System.Text;
using System.Configuration;



using System.IO;

using System.Net.Http;
using System.Net;
using Newtonsoft.Json;
using System.Data.SqlClient;
using Newtonsoft.Json.Linq;

using StudentManagementAPI.Models;

using StudentManagementAPI.services;
using System.Threading.Tasks;
using StudentManagementAPI.Data;
using Microsoft.EntityFrameworkCore;

namespace StudentManagementAPI.service
{


    public class RsaHelper
    {
        private readonly TestContext _context;
        public RsaHelper(TestContext context)
        {
            _context = context;
        }
        private static readonly string Key = "HwUnitedGroup@20";
        public string Encrypt2023(string plainText)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(Key);
            byte[] ivBytes;

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.GenerateIV(); // Generate a random IV
                ivBytes = aes.IV;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                byte[] encryptedBytes = null;

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream())
                {
                    using (CryptoStream cs = new CryptoStream(ms, encryptor, CryptoStreamMode.Write))
                    {
                        byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                        cs.Write(plainBytes, 0, plainBytes.Length);
                    }
                    encryptedBytes = ms.ToArray();
                }

                // Combine IV and encrypted data for storage
                byte[] combinedBytes = new byte[ivBytes.Length + encryptedBytes.Length];
                Buffer.BlockCopy(ivBytes, 0, combinedBytes, 0, ivBytes.Length);
                Buffer.BlockCopy(encryptedBytes, 0, combinedBytes, ivBytes.Length, encryptedBytes.Length);

                return Convert.ToBase64String(combinedBytes);
            }
        }
        public string Decrypt2023(string encryptedText)
        {
            byte[] keyBytes = Encoding.UTF8.GetBytes(Key);
            byte[] combinedBytes = Convert.FromBase64String(encryptedText);

            byte[] ivBytes = new byte[16];
            byte[] encryptedBytes = new byte[combinedBytes.Length - ivBytes.Length];

            Buffer.BlockCopy(combinedBytes, 0, ivBytes, 0, ivBytes.Length);
            Buffer.BlockCopy(combinedBytes, ivBytes.Length, encryptedBytes, 0, encryptedBytes.Length);

            using (Aes aes = Aes.Create())
            {
                aes.Key = keyBytes;
                aes.IV = ivBytes;
                aes.Padding = PaddingMode.PKCS7; // Set the padding mode explicitly

                ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                byte[] decryptedBytes = null;

                using (System.IO.MemoryStream ms = new System.IO.MemoryStream(encryptedBytes))
                using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
                using (System.IO.MemoryStream msDecrypt = new System.IO.MemoryStream())
                {
                    int read;
                    byte[] buffer = new byte[1024];

                    while ((read = cs.Read(buffer, 0, buffer.Length)) > 0)
                    {
                        msDecrypt.Write(buffer, 0, read);
                    }

                    decryptedBytes = msDecrypt.ToArray();
                }

                return Encoding.UTF8.GetString(decryptedBytes);
            }
        }
        public string GetAccessToken()
        {// Get the record from the database by id
            setting record = _context.Settings.Find(1);


            // Check if the datetime field is older than 24 hours
            if ((DateTime.Now - record.last).TotalHours > 24)
            {


                string tokenEndpoint = "https://login.salesforce.com/services/oauth2/token";
                string grantType = "password";
                string clientId = "cid";
                string clientSecret = "sid";
                string username = "<EMAIL>";
                string password = "yhyasoft";
                string token = "";


                username = record.username;

                password = Decrypt2023(Convert.ToString(record.password)) + Decrypt2023(Convert.ToString(record.mpassword));

                clientId = Convert.ToString(record.client_id);






                clientSecret = Decrypt2023(Convert.ToString(record.client_secret));



                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls
                       | SecurityProtocolType.Tls11
                       | SecurityProtocolType.Tls12
                       | SecurityProtocolType.Ssl3;

                var request = (HttpWebRequest)WebRequest.Create(tokenEndpoint);
                request.Method = "POST";

                var postData = new Dictionary<string, string>
        {
            { "grant_type", grantType },
            { "client_id", clientId },
            { "client_secret", clientSecret },
            { "username", username },
            { "password", password }
        };

                string requestBody = string.Join("&", postData
                    .Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value)}"));

                byte[] requestBodyBytes = Encoding.UTF8.GetBytes(requestBody);

                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = requestBodyBytes.Length;

                using (var requestStream = request.GetRequestStream())
                {
                    requestStream.Write(requestBodyBytes, 0, requestBodyBytes.Length);
                }

                try
                {
                    using (var response = request.GetResponse())
                    using (var responseStream = response.GetResponseStream())
                    using (var reader = new StreamReader(responseStream))
                    {
                        string jsonResponse = reader.ReadToEnd();
                        var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonResponse);
                        token = tokenResponse.access_token;
                        // Update the datetime field to the current time
                        record.last = DateTime.Now.AddDays(1);

                        // Update the key in another column (replace 'YourOtherColumn' with actual column name)
                        record.value = token; // Update with the key you need

                        // Save changes to the database
                        _context.SaveChanges();

                        return token;

                    }
                }
                catch (WebException ex)
                {
                    if (ex.Response is HttpWebResponse)
                    {
                        HttpWebResponse errorResponse = (HttpWebResponse)ex.Response;
                        using (var errorStream = errorResponse.GetResponseStream())
                        {
                            using (var reader = new StreamReader(errorStream))
                            {
                                string errorResponseText = reader.ReadToEnd();
                                throw new Exception($"Error: {errorResponseText}");
                            }
                        }
                    }

                    throw;
                }
            }
            else {
                return record.value;
            }

            //}
        }
   
     

        private class TokenResponse
        {
            public string access_token { get; set; }
            public string instance_url { get; set; }
            public string id { get; set; }
            public string token_type { get; set; }
            public string issued_at { get; set; }
            public string signature { get; set; }
        }


   
    }
}
