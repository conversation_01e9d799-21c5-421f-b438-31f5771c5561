﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.Identity.Client;
using System.Data;

namespace StudentManagementAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DatabaseController : ControllerBase
    {
        private readonly string _connectionString;

        public DatabaseController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("UE");
        }

        [HttpGet("account/search")]
        public async Task<IActionResult> SearchAccount([FromQuery] string searchTerm = "", string AgencyId = "")
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("[dbo].[procAccount_Search]", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // Add parameter - adjust based on your stored procedure parameters
                command.Parameters.AddWithValue("@SearchTerm", searchTerm ?? "");
                command.Parameters.AddWithValue("@AgencyId", AgencyId ?? "");
                await connection.OpenAsync();
                using var reader = await command.ExecuteReaderAsync();

                var results = new List<Dictionary<string, object>>();
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        row[reader.GetName(i)] = reader.GetValue(i) ?? DBNull.Value;
                    }
                    results.Add(row);
                }

                return Ok(results);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
        [HttpGet("getaccount")]
        public async Task<IActionResult> GetAccount([FromQuery] string PhoneNumber)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("[dbo].[SP_GetAccountNumberById]", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // Add the required parameter (Id)
                command.Parameters.AddWithValue("@PhoneNumber", PhoneNumber ?? "");

                await connection.OpenAsync();

                using var reader = await command.ExecuteReaderAsync();

                var results = new List<Dictionary<string, object>>();
                while (await reader.ReadAsync())
                {
                    var row = new Dictionary<string, object>();
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        row[reader.GetName(i)] = reader.GetValue(i) ?? DBNull.Value;
                    }
                    results.Add(row);
                }

                return Ok(results);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }


        [HttpPost("eventattendee")]
        public async Task<IActionResult> InsertEventAttendee([FromBody] EventAttendeeRequest request)
        {
            try
            {
                using var connection = new SqlConnection(_connectionString);
                using var command = new SqlCommand("[dbo].[procEventAttendee_Insert]", connection)
                {
                    CommandType = CommandType.StoredProcedure
                };

                // Add parameters - adjust based on your stored procedure parameters
                command.Parameters.AddWithValue("@EventId", request.EventId);
                command.Parameters.AddWithValue("@AccountId", request.AccountId);
                command.Parameters.AddWithValue("@NumberOfcompanions", request.NumberOfcompanions);
         

                // Add output parameter if your procedure returns an ID
              

                await connection.OpenAsync();
                await command.ExecuteNonQueryAsync();

                
                return Ok(new { success = true, id = "newId" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }

    // Request model for the insert endpoint
    public class EventAttendeeRequest
    {
        public string EventId { get; set; }
    //    public string AccountId { get; set; }
        public string AccountId { get; set; }
        public int NumberOfcompanions { get; set; }

    }
}
