﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using StudentManagementAPI.service.Model.Salesforce;
using System.Net.Http;
using System.Threading;

namespace StudentManagementAPI.services.Helpers
{
    public class SalesforceAPIHelper : ISalesforceAPIHelper
    {
        public SalesforceAPIHelper()
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;

            InitializeClient();
        }


        private void InitializeClient()
        {
            _apiClient = new HttpClient();
            _apiClient.DefaultRequestHeaders.Accept.Clear();
        }

        //We created the following property so that we can get access to the
        //_apiClient from other classes
        private HttpClient _apiClient;
        public HttpClient ApiClient
        {
            get
            {
                return _apiClient;
            }
        }

        public async Task<SalesforceTokenModel> GetSalesforceAccessToken(SalesforceClientModel clientModel, CancellationToken cancellationToken)
        {
            SalesforceTokenModel output = new();

            var body = new FormUrlEncodedContent(new[]
            {
                    new KeyValuePair<string, string>("grant_type", "password"),
                    new KeyValuePair<string, string>("client_id", clientModel.ClientId),
                    new KeyValuePair<string, string>("client_secret", clientModel.ClientSecret),
                    new KeyValuePair<string, string>("username", clientModel.Username),
                    new KeyValuePair<string, string>("password", clientModel.Password + clientModel.Token)
            });

            body.Headers.Add("X-PreetyPrint", "1");

            using (HttpResponseMessage response = await _apiClient.PostAsync(clientModel.LoginEndpoint, body, cancellationToken))
            {
                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadAsStringAsync();
                    var values = JsonConvert.DeserializeObject<Dictionary<string, string>>(result);

                    //For further use of ApiClient
                    AddBearerTokenToTheApiClientHeader(values["access_token"]);

                    output = new()
                    {
                        Access_Token = values["access_token"],
                        InstanceUrl = values["instance_url"],
                        UserName = clientModel.Username
                    };
                }
                else
                {
                    throw new Exception(response.ReasonPhrase);
                }
            }

            return output;
        }

        void AddBearerTokenToTheApiClientHeader(string token)
        {
            _apiClient.DefaultRequestHeaders.Clear();
            _apiClient.DefaultRequestHeaders.Accept.Clear();

            _apiClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");
            _apiClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));


            //_apiClient.DefaultRequestHeaders.Add("Sforce-Enable-PKChunking", "chunkSize=10000");
            //_apiClient.DefaultRequestHeaders.Add("Sforce-Auto-Assign", "true");
        }
    }
}
