using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Net;

namespace StudentManagementAPI.Security
{
    public static class SecurityConfig
    {
        // Add rate limiting services
        public static IServiceCollection AddApiSecurity(this IServiceCollection services)
        {
            // Add memory cache for rate limiting
            services.AddMemoryCache();
            
            // Add rate limiting service
            services.AddSingleton<IRateLimitService, RateLimitService>();
            
            // Add registration-specific rate limiting service
            services.AddSingleton<RegistrationRateLimitService>();
            
            return services;
        }

        // Add security middleware to the application pipeline
        public static IApplicationBuilder UseApiSecurity(this IApplicationBuilder app)
        {
            // Use rate limiting middleware
            app.UseMiddleware<RateLimitMiddleware>();
            
            // Use registration-specific rate limiting middleware
            app.UseMiddleware<RegistrationRateLimitMiddleware>();
            
            return app;
        }
    }

    // Interface for rate limiting service
    public interface IRateLimitService
    {
        Task<bool> IsRateLimitedAsync(string clientId);
        Task RecordRequestAsync(string clientId);
    }

    // Rate limiting service implementation
    public class RateLimitService : IRateLimitService
    {
        private readonly Dictionary<string, Queue<DateTime>> _requestStore = new Dictionary<string, Queue<DateTime>>();
        private readonly object _lock = new object();
        
        // Maximum number of requests allowed in the time window
        private const int MaxRequests = 100;
        
        // Time window in seconds
        private const int TimeWindowSeconds = 60;

        public Task<bool> IsRateLimitedAsync(string clientId)
        {
            CleanupExpiredRequests(clientId);
            
            lock (_lock)
            {
                if (!_requestStore.ContainsKey(clientId))
                {
                    return Task.FromResult(false);
                }

                return Task.FromResult(_requestStore[clientId].Count >= MaxRequests);
            }
        }

        public Task RecordRequestAsync(string clientId)
        {
            lock (_lock)
            {
                if (!_requestStore.ContainsKey(clientId))
                {
                    _requestStore[clientId] = new Queue<DateTime>();
                }

                _requestStore[clientId].Enqueue(DateTime.UtcNow);
            }

            return Task.CompletedTask;
        }

        private void CleanupExpiredRequests(string clientId)
        {
            lock (_lock)
            {
                if (!_requestStore.ContainsKey(clientId))
                {
                    return;
                }

                var queue = _requestStore[clientId];
                var cutoffTime = DateTime.UtcNow.AddSeconds(-TimeWindowSeconds);

                while (queue.Count > 0 && queue.Peek() < cutoffTime)
                {
                    queue.Dequeue();
                }

                if (queue.Count == 0)
                {
                    _requestStore.Remove(clientId);
                }
            }
        }
    }

    // Rate limiting middleware
    public class RateLimitMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IRateLimitService _rateLimitService;

        public RateLimitMiddleware(RequestDelegate next, IRateLimitService rateLimitService)
        {
            _next = next;
            _rateLimitService = rateLimitService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Get client identifier (IP address or API key if available)
            string clientId = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            
            // Check if user is authenticated and use their ID instead
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                clientId = context.User.Identity.Name ?? clientId;
            }

            // Check if client has exceeded rate limit
            if (await _rateLimitService.IsRateLimitedAsync(clientId))
            {
                context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                context.Response.Headers.Append("Retry-After", TimeSpan.FromSeconds(60).TotalSeconds.ToString());
                await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                return;
            }

            // Record this request
            await _rateLimitService.RecordRequestAsync(clientId);

            // Continue processing the request
            await _next(context);
        }
    }
}