using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StudentManagementAPI.Models
{
    [Table("university_majors")]
    public class UniversityMajor
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Column("id")]
        public int Id { get; set; }

        [Required]
        [StringLength(3)]
        [Column("holland_code")]
        public string HollandCode { get; set; } = string.Empty;

        [Column("code_description")]
        public string? CodeDescription { get; set; }

        [Column("suggested_jobs")]
        public string? SuggestedJobs { get; set; }

        [Column("majors_arabic")]
        public string? MajorsArabic { get; set; }

        [Column("majors_english")]
        public string? MajorsEnglish { get; set; }

        [Column("majors_persian")]
        public string? MajorsPersian { get; set; }

        [Column("majors_russian")]
        public string? MajorsRussian { get; set; }
    }
}
