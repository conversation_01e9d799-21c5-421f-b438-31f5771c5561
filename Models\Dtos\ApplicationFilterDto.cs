namespace StudentManagementAPI.Models.Dtos
{
    public class ApplicationFilterDto
    {
        public string? ApplicantName { get; set; }
        public string? UniversityName { get; set; }
        public string? ProgramName { get; set; }
        public string? Stage { get; set; }
        public string? Mobile { get; set; }
        public string? AcademicYear { get; set; }
        public string? Semester { get; set; }
        public string? UniversityPinCode { get; set; }
        public string? ApplicationStatus { get; set; }
        public string? Id { get; set; }
        public string? Scholarship { get; set; }
        public string? SubAgencyId { get; set; }
        public bool? HasOfferLetter { get; set; }
        public bool? HasAcceptanceLetter { get; set; }
    }
}
