internal class RateLimitMiddleware
{
    private readonly RequestDelegate _next;

    public RateLimitMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Skip rate limiting for authentication endpoints
        if (context.Request.Path.StartsWithSegments("/callback") ||
            context.Request.Path.StartsWithSegments("/google-login") ||
            context.Request.Path.StartsWithSegments("/signin-"))
        {
            await _next(context);
            return;
        }

        // Get client identifier (IP address or API key if available)
        string clientId = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        
    }
}
