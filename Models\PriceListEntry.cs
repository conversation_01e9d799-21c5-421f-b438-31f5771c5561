﻿using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Models
{
    public class PriceListEntry
    {
        [Key]
        public string Id { get; set; }
        public string Unilogo { get; set; }
        public string University_Name { get; set; }
        public string Program_Degree { get; set; }
        public string Program_Name { get; set; }
        public string Alternative_Program_Name { get; set; }
        public string University_NameAr { get; set; }
        public string University_NameRu { get; set; }
        public string University_NameFa { get; set; }
        public string CurrencyType { get; set; }
        public string Tuition_Fee__c { get; set; }
        public string Discounted_Tuition_Fee__c { get; set; }
        public string Cash_Payment_Fee__c { get; set; }
        public string Prep_School_Fee__c { get; set; }
        public string Deposit_Price__c { get; set; }
        public string Language { get; set; }
        public string Campus { get; set; }
        public string Semester { get; set; }
        public string Academic_Year { get; set; }
        public string Program { get; set; }
   
        public string ProgramFa { get; set; }
        public string ProgramRu { get; set; }
        public string University_Id { get; set; }
        public string University_Account_Billing_Country { get; set; }
        public string Term_Settings { get; set; }

        public bool Passive { get; set; }
        public bool Term_is_Active { get; set; }
    }

}
