using Azure.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StudentManagementAPI.Data;
using StudentManagementAPI.Models;
using StudentManagementAPI.service;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Tasks;

namespace StudentManagementAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class TestAttemptsController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILanguageService _languageService;
        private readonly getuni2023 _uniService;
        // Supported languages
        private readonly string[] _supportedLanguages = new[] { "en", "ar", "ru", "fa" };
        private const string DEFAULT_LANGUAGE = "en";

        public TestAttemptsController(ApplicationDbContext context, UserManager<ApplicationUser> userManager, ILanguageService languageService, getuni2023 uniService)
        {
            _uniService = uniService;
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _languageService = languageService ?? throw new ArgumentNullException(nameof(languageService));
        }

        // Helper method to validate language parameter
        private string ValidateLanguage(string lang)
        {
            if (string.IsNullOrEmpty(lang) || !_supportedLanguages.Contains(lang.ToLower()))
            {
                return DEFAULT_LANGUAGE;
            }
            return lang.ToLower();
        }

        private async Task<string> GetCurrentStudentId()
        {
            if (!User.Identity.IsAuthenticated)
                return null;

            var username = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(username))
                return null;

            var user = await _userManager.FindByNameAsync(username); // ✅ التصليحة هون

            if (user == null)
                return null;

            var student = await _context.Students
                .FirstOrDefaultAsync(s => s.Id == user.Id);

            return student?.Id;
        }


        // GET: api/TestAttempts
        [HttpGet]
        public async Task<ActionResult<IEnumerable<TestAttempt>>> GetTestAttempts([FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                var studentId = await GetCurrentStudentId();
                if (studentId == null)
                {
                    return Unauthorized(new ApiResponse
                    {
                        Status = false,
                        Code = 401,
                        Message = _languageService.GetMessage("Unauthorized", language),
                        Language = language
                    });
                }

                var testAttempts = await _context.TestAttempts
                    .Where(t => t.StudentId == studentId)
                    .Select(t => new
                    {
                        t.Id,
                        t.StartTime,
                        t.EndTime,
                        t.IsCompleted,
                        t.Status,
                        t.Result,
                        t.Name,
                        t.TestType,
                        t.TotalQuestions,
                        t.CompletionPercentage,
                        t.LastResumeTime,
                        AnswerCount = t.Answers.Count,
                        IsTestCompleted = (t.TestType.ToLower() == "small" && t.Answers.Count >= 60) ||
                                       (t.TestType.ToLower() == "full" && t.Answers.Count >= 120),
                        IsSmallTest = t.TestType.ToLower() == "small",
                        TestUrl = ((t.TestType.ToLower() == "small" && t.Answers.Count >= 60) ||
                                 (t.TestType.ToLower() == "full" && t.Answers.Count >= 120))
                            ? $"/portal/holland-test-results?testAttemptId={t.Id}"
                            : $"/portal/holland-test?testAttemptId={t.Id}"
                    })
                    .ToListAsync();



                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Data = testAttempts,
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = "InternalServerError",
                    Language = lang
                });
            }
        }

        // GET: api/TestAttempts/5
        [HttpGet("{id}")]
        public async Task<ActionResult<object>> GetTestAttempt(int id, [FromQuery] string testType = "full", [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            // Validate language
            lang = ValidateLanguage(lang);

            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", lang),
                    Language = lang
                });
            }

            // Fetch TestAttempt without including Student from TestContext
            var testAttempt = await _context.TestAttempts
                .Include(t => t.Answers)
                    .ThenInclude(a => a.Question)
                .FirstOrDefaultAsync(t => t.Id == id);

            if (testAttempt == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Test attempt not found"
                });
            }

            if (testAttempt.StudentId != studentId)
            {
                return Forbid();
            }

            // Get all active questions
            var activeQuestions = await _context.Questions
                .Where(q => q.IsActive)
                .OrderBy(q => q.OrderNumber)
                .ToListAsync();

            // Filter questions based on test type
            List<Question> selectedQuestions;
            if (testType.ToLower() == "small")
            {
                // For small test, select 10 questions per personality type
                var personalityTypes = activeQuestions.Select(q => q.PersonalityTypeId).Distinct().ToList();
                selectedQuestions = new List<Question>();

                foreach (var typeId in personalityTypes)
                {
                    var questionsOfType = activeQuestions.Where(q => q.PersonalityTypeId == typeId).Take(10).ToList();
                    selectedQuestions.AddRange(questionsOfType);
                }
            }
            else
            {
                // For full test, use all active questions
                selectedQuestions = activeQuestions;
            }

            // Create a response object with test attempt details and questions
            var response = new
            {
                testAttempt.Id,
                testAttempt.Name,
                testAttempt.StartTime,
                testAttempt.EndTime,
                testAttempt.Status,
                testAttempt.IsCompleted,
                testAttempt.CompletionPercentage,
                testAttempt.Result,
                Questions = selectedQuestions.Select(q => new
                {
                    q.Id,
                    q.TextAr,
                    q.PersonalityTypeId,
                    q.OrderNumber,
                    Answer = testAttempt.Answers.FirstOrDefault(a => a.QuestionId == q.Id)?.Response
                }).ToList()
            };

            return response;
        }

        // PUT: api/TestAttempts/5/pause
        [HttpPut("{id}/pause")]
        public async Task<IActionResult> PauseTestAttempt(int id, [FromQuery] string lang = "en")
        {
            try
            {
                var language = _languageService.ValidateLanguage(lang);
                var studentId = await GetCurrentStudentId();
                if (studentId == null)
                {
                    return Unauthorized(new ApiResponse
                    {
                        Status = false,
                        Code = 401,
                        Message = _languageService.GetMessage("Unauthorized", language),
                        Language = language
                    });
                }

                var testAttempt = await _context.TestAttempts.FindAsync(id);
                if (testAttempt == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = _languageService.GetMessage("TestNotFound", language),
                        Language = language
                    });
                }

                testAttempt.Status = "Paused";
                await _context.SaveChangesAsync();

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Message = _languageService.GetMessage("Success", language),
                    Language = language
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new ApiResponse
                {
                    Status = false,
                    Code = 500,
                    Message = _languageService.GetMessage("InternalServerError", lang),
                    Language = lang
                });
            }
        }

        // PUT: api/TestAttempts/5/resume
        [HttpPut("{id}/resume")]
        public async Task<IActionResult> ResumeTestAttempt(int id, [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", lang),
                    Language = lang
                });
            }
            // Validate language
            lang = ValidateLanguage(lang);

            var testAttempt = await _context.TestAttempts.FindAsync(id);
            if (testAttempt == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = _languageService.GetMessage("TestAttemptNotFoundOrNotAuthorized", lang)
                });
            }

            if (testAttempt.StudentId != studentId)
            {
                return Forbid(new ApiResponse
                {
                    Status = false,
                    Code = 403,
                    Message = _languageService.GetMessage("Forbidden", lang)
                }.ToString());
            }

            testAttempt.Status = "InProgress";
            testAttempt.LastResumeTime = DateTime.Now;

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TestAttemptExists(id))
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = "Test attempt not found"
                    });
                }
                throw;
            }
        }

        // PUT: api/TestAttempts/5/name
        [HttpPut("{id}/name")]
        public async Task<IActionResult> UpdateTestName(int id, [FromBody] string name, [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            // Validate language
            lang = ValidateLanguage(lang);
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", lang),
                    Language = lang
                });
            }
            var testAttempt = await _context.TestAttempts.FindAsync(id);
            if (testAttempt == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Test attempt not found"
                });
            }

            if (testAttempt.StudentId != studentId)
            {
                return Forbid();
            }

            testAttempt.Name = name;

            try
            {
                await _context.SaveChangesAsync();
                return NoContent();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!TestAttemptExists(id))
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = "Test attempt not found"
                    });
                }
                throw;
            }
        }

        //// PUT: api/TestAttempts/5/complete
        //[HttpPut("{id}/complete")]
        //public async Task<IActionResult> CompleteTestAttempt(int id, [FromBody] string result, [FromQuery] string lang = "en")
        //{
        //    try
        //    {
        //        var language = _languageService.ValidateLanguage(lang);
        //        var studentId = await GetCurrentStudentId();
        //        if (studentId == null)
        //        {
        //            return Unauthorized(new ApiResponse
        //            {
        //                Status = false,
        //                Code = 401,
        //                Message = _languageService.GetMessage("Unauthorized", language),
        //                Language = language
        //            });
        //        }

        //        var testAttempt = await _context.TestAttempts.FindAsync(id);
        //        if (testAttempt == null)
        //        {
        //            return NotFound(new ApiResponse
        //            {
        //                Status = false,
        //                Code = 404,
        //                Message = _languageService.GetMessage("TestAttemptNotFound", language),
        //                Language = language
        //            });
        //        }

        //        if (testAttempt.StudentId != studentId)
        //        {
        //           new ApiResponse
        //            {
        //                Status = false,
        //                Code = 403,
        //                Message = _languageService.GetMessage("Forbidden", language),
        //                Language = language
        //            };
        //        _context.Answers.Add(answer);

        //        // 7. Recalculate completion percentage
        //        var totalAnswers = await _context.Answers
        //            .CountAsync(a => a.TestAttemptId == testAttemptId);

        //        testAttempt.CompletionPercentage = testAttempt.TotalQuestions > 0
        //            ? totalAnswers * 100.0 / testAttempt.TotalQuestions
        //            : 0;

        //        // 8. Persist
        //        await _context.SaveChangesAsync();

        //        // 9. Return the new/updated Answer.Id + percentage
        //        var result = new SaveAnswerResult
        //        {
        //            AnswerId = answer.Id,
        //            CompletionPercentage = testAttempt.CompletionPercentage
        //        };

        //        return Ok(new ApiResponse<SaveAnswerResult>
        //        {
        //            Status = true,
        //            Code = 200,
        //            Message = "Answer saved successfully",
        //            Data = result
        //        });
        //    }
        //    else
        //    {
        //        }

        //        testAttempt.IsCompleted = true;
        //        testAttempt.Status = "Completed";
        //        testAttempt.EndTime = DateTime.UtcNow;
        //        testAttempt.Result = result;
        //        testAttempt.CompletionPercentage = 100;

        //        await _context.SaveChangesAsync();

        //        return Ok(new ApiResponse
        //        {
        //            Status = true,
        //            Code = 200,
        //            Message = _languageService.GetMessage("TestAttemptCompleted", language),
        //            Language = language
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new ApiResponse
        //        {
        //            Status = false,
        //            Code = 500,
        //            Message = _languageService.GetMessage("InternalServerError", lang),
        //            Language = lang
        //        });
        //    }
        //}

        // PUT: api/TestAttempts/{testAttemptId}/answers/{questionId}
        [HttpPut("SaveAnswer")]
        public async Task<IActionResult> UpdateAnswer([FromBody] UpdateAnswerRequest request)
        {
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = "User is not associated with a student account"
                });
            }

            var testAttempt = await _context.TestAttempts
                .Include(t => t.Answers)
                .FirstOrDefaultAsync(t => t.Id == request.TestAttemptId);

            if (testAttempt == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Test attempt not found"
                });
            }

            if (testAttempt.StudentId != studentId)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Test attempt not found"
                });
            }

            if (testAttempt.IsCompleted)
            {
                return BadRequest(new ApiResponse
                {
                    Status = false,
                    Code = 400,
                    Message = "Cannot update answers for a completed test"
                });
            }

            var question = await _context.Questions.FindAsync(request.questionId);
            if (question == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Question not found"
                });
            }

            var answer = testAttempt.Answers.FirstOrDefault(a => a.QuestionId == request.questionId);
            if (answer == null)
            {
                // Create a new answer if it doesn't exist
                answer = new Answer
                {
                    Language = request.Language,
                    TestAttemptId = request.TestAttemptId,
                    QuestionId = request.questionId,
                    Response = request.Value,
                    code = request.Code, // Initialize with empty string to avoid null value exception
                    AnsweredAt = DateTime.Now // Ensure DateTime is initialized

                };
                _context.Answers.Add(answer);
            }
            else
            {
                // Update existing answer
                answer.Response = request.Value;
                answer.code = !string.IsNullOrEmpty(request.Code) ? request.Code : string.Empty;

                if (answer.AnsweredAt == default(DateTime))
                {
                    answer.AnsweredAt = DateTime.Now;
                }
            }

            // Calculate completion percentage
            var totalAnswers = await _context.Answers
                .CountAsync(a => a.TestAttemptId == request.TestAttemptId);

            var newCompletionPercentage = testAttempt.TotalQuestions > 0
                ? totalAnswers * 100.0 / testAttempt.TotalQuestions
                : 0;

            // Update completion percentage
            testAttempt.CompletionPercentage = newCompletionPercentage;

            // Update status based on completion percentage
            if (Math.Abs(newCompletionPercentage - 100) < 0.01) // Account for floating point precision
            {
                testAttempt.Status = "Completed";
                testAttempt.IsCompleted = true;
                testAttempt.EndTime = DateTime.UtcNow;
            }
            else if (testAttempt.Status != "InProgress" && testAttempt.Status != "Paused")
            {
                testAttempt.Status = "InProgress";
            }

            // Persist changes
            await _context.SaveChangesAsync();

            // Return result
            var result = new SaveAnswerResult
            {
                AnswerId = answer.Id,
                CompletionPercentage = testAttempt.CompletionPercentage
            };

            return Ok(new ApiResponse<SaveAnswerResult>
            {
                Status = true,
                Code = 200,
                Message = "Answer saved successfully",
                Data = result
            });
        }
        [HttpGet("results/{testAttemptId}")]
        public async Task<IActionResult> GetResults(int testAttemptId)
        {
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = "User is not associated with a student account"
                });
            }

            // Fetch the test attempt along with answers
            var testAttempt = await _context.TestAttempts
                .Include(t => t.Answers)
                .FirstOrDefaultAsync(t => t.Id == testAttemptId && t.StudentId == studentId);

            if (testAttempt == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Test attempt not found"
                });
            }

            if (testAttempt.Answers.Count != testAttempt.TotalQuestions)
            {
                return BadRequest(new ApiResponse
                {
                    Status = false,
                    Code = 400,
                    Message = "The number of answers does not match the total questions"
                });
            }

            // Group answers by code
            var grouped = testAttempt.Answers
                .Where(a => !string.IsNullOrEmpty(a.code))
                .GroupBy(a => a.code)
                .Select(g => new
                {
                    Code = g.Key,
                    Total = g.Sum(a => long.TryParse(a.Response, out var value) ? value : 0)
                })
                .ToList();

            var totalSum = grouped.Sum(g => g.Total);

            // Add percentage and sort descending
            var results = grouped
                .Select(g => new
                {
                    Code = g.Code,
                    Total = g.Total,
                    Percentage = totalSum > 0 ? Math.Round((g.Total * 100.0) / totalSum, 2) : 0
                })
                .OrderByDescending(x => x.Total)
                .ToList();

            var resultCodes = results.Select(r => r.Code).ToList();

            // Top 3 Holland Codes
            var top3Codes = results.Take(3).Select(r => r.Code).ToList();
            var top3Codesc = string.Join("", results.Take(3).Select(r => r.Code));

            // Fetch full personality details including all languages
            var personalityDetails = await _context.PersonalityTypes
                .Where(pt => resultCodes.Contains(pt.Code))
                .Select(pt => new
                {
                    pt.Code,

                    // Names
                    pt.NameAr,
                    pt.NameEn,
                    pt.NameFa,
                    pt.NameRu,

                    // Descriptions
                    pt.DescriptionAr,
                    pt.DescriptionEn,
                    pt.DescriptionFa,
                    pt.DescriptionRu,

                    // Traits
                    pt.TraitsAr,
                    pt.TraitsEn,
                    pt.TraitsFa,
                    pt.TraitsRu,

                    // Work Environment
                    pt.WorkEnvironmentAr,
                    pt.WorkEnvironmentEn,
                    pt.WorkEnvironmentFa,
                    pt.WorkEnvironmentRu,

                    // Preferred Activities
                    pt.PreferredActivitiesAr,
                    pt.PreferredActivitiesEn,
                    pt.PreferredActivitiesFa,
                    pt.PreferredActivitiesRu,

                    // Strengths
                    pt.StrengthsAr,
                    pt.StrengthsEn,
                    pt.StrengthsFa,
                    pt.StrengthsRu,

                    // Skills Needed
                    pt.SkillsNeededAr,
                    pt.SkillsNeededEn,
                    pt.SkillsNeededFa,
                    pt.SkillsNeededRu,

                    // General Activities
                    pt.ActivitiesAr,
                    pt.ActivitiesEn,
                    pt.ActivitiesFa,
                    pt.ActivitiesRu,

                    // Icon
                    pt.Icon
                })
                .ToListAsync();

            // Combine with scores and percentages
            var detailedResults = results.Select(r => new
            {
                Code = r.Code,
                Total = r.Total,
                Percentage = r.Percentage,

                // Include all translations from personalityDetails
                Details = personalityDetails.FirstOrDefault(pt => pt.Code == r.Code)
            }).ToList();

            // Determine majors based on top 3 codes
            var rawMajors = await _context.UniversityMajors
                .Where(mc => top3Codesc == mc.HollandCode)
                .Select(mc => new
                {
                    mc.MajorsEnglish,
                    mc.MajorsArabic,
                    mc.MajorsRussian,
                    mc.MajorsPersian,
                    mc.HollandCode
                })
                .ToListAsync();

            var majorsForTop3 = rawMajors.Select(m => new
            {
                MajorsEnglish = m.MajorsEnglish?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()).ToArray() ?? Array.Empty<string>(),
                MajorsArabic = m.MajorsArabic?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()).ToArray() ?? Array.Empty<string>(),
                MajorsRussian = m.MajorsRussian?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()).ToArray() ?? Array.Empty<string>(),
                MajorsPersian = m.MajorsPersian?.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(s => s.Trim()).ToArray() ?? Array.Empty<string>(),
                HollandCode = m.HollandCode
            }).ToList();

            // Count total number of matching majors
            var majorCount = majorsForTop3.Count;


            // Update existing answer

            // Calculate completion percentage
            var totalAnswers = await _context.Answers
                .CountAsync(a => a.TestAttemptId == testAttemptId);

            var newCompletionPercentage = testAttempt.TotalQuestions > 0
                ? totalAnswers * 100.0 / testAttempt.TotalQuestions
                : 0;

            // Check if test is completed based on test type and answer count
            var isTestCompleted = (testAttempt.TestType.ToLower() == "small" && testAttempt.Answers.Count >= 60) ||
                                 (testAttempt.TestType.ToLower() == "full" && testAttempt.Answers.Count >= 120);

            // Update completion percentage
            testAttempt.CompletionPercentage = newCompletionPercentage;


            testAttempt.Status = "Completed";
            testAttempt.IsCompleted = true;
            testAttempt.Result = top3Codesc;


            // Persist changes
            await _context.SaveChangesAsync();
            var data = majorsForTop3.FirstOrDefault();
            string combinedMajors = string.Join(", ", data.MajorsArabic.Concat(data.MajorsEnglish));

            updateholland(top3Codesc, combinedMajors, testAttempt.SalseId);
            // Final response
            var response = new ApiResponse<dynamic>
            {
                Status = true,
                Code = 200,
                Message = "Results calculated successfully",
                Data = new
                {
                    Results = detailedResults,
                    Majors = majorsForTop3,
                    MajorCount = majorCount,
                    Top3HollandCodes = top3Codes
                }
            };

            return Ok(response);
        }
        public class MajorData
        {
            public List<string> MajorsEnglish { get; set; }
            public List<string> MajorsArabic { get; set; }
            public List<string> MajorsRussian { get; set; }
            public List<string> MajorsPersian { get; set; }
            public string HollandCode { get; set; }
        }
        public class SaveAnswerResult
        {
            public int AnswerId { get; set; }
            public double CompletionPercentage { get; set; }
        }

        [HttpGet("{id}/questions")]
        public async Task<ActionResult<ApiResponse>> GetTestQuestions(int id, [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            // Validate language
            lang = ValidateLanguage(lang);

            // Validate test attempt exists and belongs to current student
            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = "User is not associated with a student account"
                });
            }

            var testAttempt = await _context.TestAttempts
                .FirstOrDefaultAsync(t => t.Id == id && t.StudentId == studentId);

            if (testAttempt == null)
            {
                return NotFound(new ApiResponse
                {
                    Status = false,
                    Code = 404,
                    Message = "Test attempt not found or not authorized"
                });
            }

            // Get questions for this test
            var questions = new List<QuestionDto>();

            // If test type is small, get only selected questions
            if (testAttempt.TestType?.ToLower() == "small")
            {
                var personalityTypes = await _context.Questions
                    .Where(q => q.IsActive)
                    .Select(q => q.PersonalityTypeId)
                    .Distinct()
                    .ToListAsync();

                foreach (var typeId in personalityTypes)
                {
                    var questionsOfType = await _context.Questions
                        .Where(q => q.IsActive && q.PersonalityTypeId == typeId)
                        .OrderBy(q => q.OrderNumber)
                        .Take(10)
                        .Select(q => new QuestionDto
                        {
                            Id = q.Id,
                            Text = q.TextAr,
                            PersonalityTypeId = q.PersonalityTypeId.ToString(),
                            OrderNumber = q.OrderNumber
                        })
                        .ToListAsync();

                    questions.AddRange(questionsOfType);
                }
            }
            else
            {
                // For full test, get all active questions
                questions = await _context.Questions
                    .Where(q => q.IsActive)
                    .OrderBy(q => q.OrderNumber)
                    .Select(q => new QuestionDto
                    {
                        Id = q.Id,
                        Text = q.TextAr,
                        PersonalityTypeId = q.PersonalityTypeId.ToString(),
                        OrderNumber = q.OrderNumber
                    })
                    .ToListAsync();
            }

            // Get answers for this test attempt
            var answers = await _context.Answers
                .Where(a => a.TestAttemptId == id)
                .ToListAsync();

            // Add answer values to questions
            foreach (var question in questions)
            {
                var answer = answers.FirstOrDefault(a => a.QuestionId == question.Id);
                question.Answer = answer?.Response;
            }

            return Ok(new ApiResponse
            {
                Status = true,
                Code = 200,
                Message = "Questions retrieved successfully",
                Data = questions
            });
        }

        public string PersonContactId(string leadId)
        {

            //prepare the query
            string query = "SELECT PersonContactId   FROM Account WHERE  Id= '" + leadId + "'";

            //Query
            var result = _uniService.Query(query);

            JObject json = JObject.Parse(result.Result);
            string value = (string)json["records"][0]["PersonContactId"];

            return value;
        }
        [HttpPost("start")]
        public async Task<ActionResult<ApiResponse>> StartTestAttempt([FromBody] StartTestAttemptRequest request, [FromQuery] string lang = DEFAULT_LANGUAGE)
        {
            string language = !string.IsNullOrEmpty(request.Language) ? request.Language : lang;
            language = ValidateLanguage(language);

            var studentId = await GetCurrentStudentId();
            if (studentId == null)
            {
                return Unauthorized(new ApiResponse
                {
                    Status = false,
                    Code = 401,
                    Message = _languageService.GetMessage("Unauthorized", language),
                    Language = language
                });
            }

            // ✅ Case: Existing TestAttemptId – load questions that were originally selected for this test attempt
            if (request.TestAttemptId.HasValue&& request.TestAttemptId!=0)
            {
                var testAttempt = await _context.TestAttempts
                    .Include(a => a.Answers)
                    .Include(a => a.Student)
                    .FirstOrDefaultAsync(a => a.Id == request.TestAttemptId.Value && a.StudentId == studentId);

                if (testAttempt == null)
                {
                    return NotFound(new ApiResponse
                    {
                        Status = false,
                        Code = 404,
                        Message = "TestAttemptNotFound",
                        Language = language
                    });
                }

                // Get the original questions that were selected for this test attempt
                var allQuestions = await _context.Questions
                    .Include(q => q.PersonalityTypes)
                    .Where(q => q.IsActive)
                    .OrderBy(q => q.OrderNumber)
                    .ToListAsync();

                // Recreate the original question selection logic based on the test type
                List<Question> originalQuestions;
                if (testAttempt.TestType?.ToLower() == "small")
                {
                    var personalityTypes = allQuestions.Select(q => q.PersonalityTypeId).Distinct().ToList();
                    originalQuestions = new List<Question>();

                    foreach (var typeId in personalityTypes)
                    {
                        var questionsOfType = allQuestions
                            .Where(q => q.PersonalityTypeId == typeId)
                            .Take(10)
                            .ToList();

                        originalQuestions.AddRange(questionsOfType);
                    }
                }
                else
                {
                    originalQuestions = allQuestions;
                }

                // Get answered question IDs
                var answeredQuestionIds = testAttempt.Answers.Select(a => a.QuestionId).ToHashSet();
                var answersDict = testAttempt.Answers.ToDictionary(a => a.QuestionId, a => a);

                // Separate answered and unanswered questions
                var answeredQuestions = originalQuestions.Where(q => answeredQuestionIds.Contains(q.Id)).ToList();
                var unansweredQuestions = originalQuestions.Where(q => !answeredQuestionIds.Contains(q.Id)).ToList();

                // Return answered questions with their answers + unanswered questions
                var questionsDto = new List<QuestionDto>();

                // Add answered questions with their responses
                questionsDto.AddRange(answeredQuestions.Select(q => new QuestionDto
                {
                    code = q.PersonalityTypes?.Code,
                    Id = q.Id,
                    Text = language switch
                    {
                        "ar" => q.TextAr,
                        "en" => q.TextEn,
                        "fa" => q.TextFa,
                        "ru" => q.TextRu,
                        _ => q.TextEn
                    },
                    PersonalityTypeId = q.PersonalityTypeId.ToString(),
                    OrderNumber = q.OrderNumber,
                    Answer = answersDict[q.Id].Response
                }));

                // Add unanswered questions without responses
                questionsDto.AddRange(unansweredQuestions.Select(q => new QuestionDto
                {
                    code = q.PersonalityTypes?.Code,
                    Id = q.Id,
                    Text = language switch
                    {
                        "ar" => q.TextAr,
                        "en" => q.TextEn,
                        "fa" => q.TextFa,
                        "ru" => q.TextRu,
                        _ => q.TextEn
                    },
                    PersonalityTypeId = q.PersonalityTypeId.ToString(),
                    OrderNumber = q.OrderNumber,
                    Answer = null
                }));

                // Sort by order number to maintain original sequence
                questionsDto = questionsDto.OrderBy(q => q.OrderNumber).ToList();

                return Ok(new ApiResponse
                {
                    Status = true,
                    Code = 200,
                    Message = "AnswersLoaded",
                    Data = new
                    {
                        TestAttemptId = testAttempt.Id,
                        Questions = questionsDto,
                        TotalQuestions = questionsDto.Count,
                        AnsweredQuestions = answeredQuestions.Count,
                        RemainingQuestions = unansweredQuestions.Count
                    },
                    Language = language
                });
            }

            // ✅ Case: New test attempt
            var activeQuestions = await _context.Questions
                .Include(q => q.PersonalityTypes)
                .Where(q => q.IsActive)
                .OrderBy(q => q.OrderNumber)
                .ToListAsync();

            if (!activeQuestions.Any())
            {
                return BadRequest(new ApiResponse
                {
                    Status = false,
                    Code = 400,
                    Message = "No active questions available for the test",
                    Language = language
                });
            }

            List<Question> selectedQuestions;
            if (request.TestType?.ToLower() == "small")
            {
                var personalityTypes = activeQuestions.Select(q => q.PersonalityTypeId).Distinct().ToList();
                selectedQuestions = new List<Question>();

                foreach (var typeId in personalityTypes)
                {
                    var questionsOfType = activeQuestions
                        .Where(q => q.PersonalityTypeId == typeId)
                        .Take(10)
                        .ToList();

                    selectedQuestions.AddRange(questionsOfType);
                }
            }
            else
            {
                selectedQuestions = activeQuestions;
            }

            TestAttempt testAttemptNew = new TestAttempt();
            if (request.TestAttemptId == 0)
            {
                testAttemptNew = new TestAttempt
                {
                    TestType = request.TestType,
                    StudentId = studentId,
                    Name = request.TestName ?? "Personality Test",
                    StartTime = DateTime.Now,
                    Status = "InProgress",
                    IsCompleted = false,
                    CompletionPercentage = 0,
                    TotalQuestions = selectedQuestions.Count,
                };

                _context.TestAttempts.Add(testAttemptNew);
                await _context.SaveChangesAsync();
            }
            else
            {
                testAttemptNew = _context.TestAttempts.FirstOrDefault(a => a.Id == request.TestAttemptId);
            }

            var questionsDtoNew = selectedQuestions.Select(q => new QuestionDto
            {
                code = q.PersonalityTypes?.Code,
                Id = q.Id,
                Text = language switch
                {
                    "ar" => q.TextAr,
                    "en" => q.TextEn,
                    "fa" => q.TextFa,
                    "ru" => q.TextRu,
                    _ => q.TextEn
                },
                PersonalityTypeId = q.PersonalityTypeId.ToString(),
                OrderNumber = q.OrderNumber,
                Answer = null
            }).ToList();
            var student = _context.Students
                .Where(s => s.Id == studentId)
                .Select(s => s.CrmId).SingleOrDefault();
            string contactId = PersonContactId(student);
          var responseFromServer=  await inserttoholland("", contactId, "", "", testAttemptNew.Id.ToString());
            JObject jsonObject = JObject.Parse(responseFromServer);

            string salceid = (string)jsonObject["id"];
            var testAttemptv = await _context.TestAttempts
            
              .FirstOrDefaultAsync(t => t.Id == testAttemptNew.Id && t.StudentId == studentId);
       
            testAttemptv.SalseId = salceid;


            // Persist changes
            await _context.SaveChangesAsync();
            return Ok(new ApiResponse
            {
                Status = true,
                Code = 201,
                Message = "Test started successfully",
                Data = new
                {
                    TestAttemptId = testAttemptNew.Id,
                    Questions = questionsDtoNew
                },
                Language = language
            });
        }

        private bool TestAttemptExists(int id)
        {
            return _context.TestAttempts.Any(e => e.Id == id);
        }

        public async Task<string> inserttoholland(string Selected_Result_Programs__c, string ContactId__c, string Result_Code__c, string Result_Programs__c, string External_Id__c)
        {
            string sObject = "Holland_Test__c";
            object body = new
            {
                Selected_Result_Programs__c = Selected_Result_Programs__c,
                ContactId__c = ContactId__c,
                Result_Code__c = Result_Code__c,
                Result_Programs__c = Result_Programs__c,
                External_Id__c = External_Id__c
            };

            return await _uniService.Insert(sObject, body);
        }
        public async Task<string> updateholland( string Result_Code__c, string Result_Programs__c,string id)
        {
            string sObject = "Holland_Test__c";
            object body = new
            {
                            Result_Code__c = Result_Code__c,
                Result_Programs__c = Result_Programs__c,
         
            };

            return await _uniService.Update(sObject,id, body);
        }
        public class StartTestAttemptRequest
        {
            public string? TestName { get; set; }
            public string TestType { get; set; } = "full";
            public string Language { get; set; } = "en";
            public int? TestAttemptId { get; set; }
        }

        public class UpdateAnswerRequest
        {
            [Required]
            public string Value { get; set; }
            public string Code { get; set; }
            public int TestAttemptId { get; set; }
            public int questionId { get; set; }
            public int? AnswerId { get; set; } = 0;
            public string Language { get; set; }
        }

        public class SaveAnswerRequest
        {
            [Required]
            public int QuestionId { get; set; }

            [Required]
            public string Value { get; set; }
            public string Code { get; set; }
            [Required]
            public int TestAttemptId { get; set; }
            public int? AnswerId { get; set; }
            public string Language { get; set; } = "en";
        }

        public class QuestionDto
        {
            public int Id { get; set; }
            public string Text { get; set; }
            public string PersonalityTypeId { get; set; }
            public string code { get; set; }
            public int OrderNumber { get; set; }
            public string Answer { get; set; }
            public string Language { get; set; }
        }

        public class ApiResponse
        {
            public bool Status { get; set; }
            public int Code { get; set; }
            public string Message { get; set; }
            public object Data { get; set; }
            public string Language { get; set; } = "en";
        }
        public class ApiResponse<T>
        {
            public bool Status { get; set; }
            public int Code { get; set; }
            public string Message { get; set; }
            public T Data { get; set; }
        }
    }
}

    // Helper method to get localized messages


