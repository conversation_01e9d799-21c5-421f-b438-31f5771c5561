{"info": {"name": "Events API", "description": "API collection for managing Events in Student Management System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get All Events", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/events", "host": ["{{baseUrl}}"], "path": ["api", "events"]}, "description": "Retrieve a list of all active events"}, "response": []}, {"name": "Get Event by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/events/1", "host": ["{{baseUrl}}"], "path": ["api", "events", "1"]}, "description": "Retrieve a specific event by its ID"}, "response": []}, {"name": "Create Event (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Sample Event\",\n  \"description\": \"This is a sample event description\",\n  \"imageURL\": \"https://example.com/event.jpg\",\n  \"location\": \"Online\",\n  \"eventDate\": \"2025-07-15T00:00:00\",\n  \"startTime\": \"2025-07-15T18:00:00\",\n  \"endTime\": \"2025-07-15T20:00:00\",\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/events", "host": ["{{baseUrl}}"], "path": ["api", "events"]}, "description": "Create a new event (Admin only)"}, "response": []}, {"name": "Update Event (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Event Title\",\n  \"description\": \"Updated event description\",\n  \"location\": \"Updated Location\"\n}"}, "url": {"raw": "{{baseUrl}}/api/events/1", "host": ["{{baseUrl}}"], "path": ["api", "events", "1"]}, "description": "Update an existing event (Admin only)"}, "response": []}, {"name": "Delete Event (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{admin<PERSON><PERSON><PERSON><PERSON>}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/events/1", "host": ["{{baseUrl}}"], "path": ["api", "events", "1"]}, "description": "Delete an event (soft delete, Admin only)"}, "response": []}], "variable": [{"key": "baseUrl", "value": "https://your-api-url.com", "type": "string"}, {"key": "authToken", "value": "your_auth_token_here", "type": "string"}, {"key": "adminAuthToken", "value": "your_admin_auth_token_here", "type": "string"}]}