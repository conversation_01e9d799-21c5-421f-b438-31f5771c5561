﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.IO;
using System.Linq;
using System.Text;

namespace StudentManagementAPI.Models
{
    public class Answer
    {
        [Key]
        public int Id { get; set; }

        public int QuestionId { get; set; }
        public virtual Question Question { get; set; }

        public int TestAttemptId { get; set; }
        public virtual TestAttempt TestAttempt { get; set; }

        [Required]
        public string Response { get; set; } = "";

        [Required]
        public string code { get; set; }

        public string? Language { get; set; } // "en" or "ar"

        [NotMapped]
        public string Value 
        { 
            get { return Response; }
            set { Response = value; }
        }

        public DateTime AnsweredAt { get; set; } = DateTime.Now;
    }
}