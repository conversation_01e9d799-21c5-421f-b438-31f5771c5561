﻿using Microsoft.EntityFrameworkCore;
using StudentManagementAPI.Models;
namespace StudentManagementAPI.Data
{
    public class UniversityProgramsDbContext : DbContext
    {
        public UniversityProgramsDbContext(DbContextOptions<UniversityProgramsDbContext> options)
            : base(options)
        {
        }

        public DbSet<UniversityProgram> UniversityProgram { get; set; }
        public DbSet<TermSetting> TermSetting { get; set; }
        public DbSet<PriceListEntry> priceListEntries { get; set; }
        public DbSet<University> University { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
          
        }
    }
}
