﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace StudentManagementAPI.service.Model.Salesforce
{
    public class SalesforceClientModel
    {
     
    

        public int Id { get; set; }
        public int ProfileId { get; set; }

        public string? LoginEndpoint { get; set; } = "https://login.salesforce.com/services/oauth2/token";
        public string? ApiEndPoint { get; set; } = "/services/data/v57.0/query";
        public string? ApiInsertEndPoint { get; set; } = "/services/data/v57.0/sobjects/";
        public string? BulkApi2Endpoint { get; set; } = "/services/data/v57.0/jobs/query";


        public string? Username { get; set; } 
        public string? Password { get; set; }
        public string? Token { get; set; }
        public string? ClientId { get; set; } 
        public string? ClientSecret { get; set; }
      
    }
}
