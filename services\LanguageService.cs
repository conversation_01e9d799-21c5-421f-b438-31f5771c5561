

public interface ILanguageService
{
    string GetMessage(string key, string language);
    string ValidateLanguage(string language);
}

public class LanguageService : ILanguageService
{
    public string GetMessage(string key, string language)
    {
        language = ValidateLanguage(language);
        
        if (LanguageConstants.Messages.TryGetValue(language, out var messages))
        {
            if (messages.TryGetValue(key, out var message))
            {
                return message;
            }
        }
        
        // Fallback to English if message not found
        return LanguageConstants.Messages["en"][key];
    }

    public string ValidateLanguage(string language)
    {
        if (string.IsNullOrEmpty(language) || !LanguageConstants.SupportedLanguages.Contains(language.ToLower()))
        {
            return LanguageConstants.DefaultLanguage;
        }
        return language.ToLower();
    }
}