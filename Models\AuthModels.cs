using System.ComponentModel.DataAnnotations;

namespace StudentManagementAPI.Models
{
    public class LoginRequest
    {
        [Required(ErrorMessage = "Email or phone number is required")]
        public string EmailOrPhone { get; set; }

        [Required]
        public string Password { get; set; }
    }
    public class phonecheck
    {
        [Required(ErrorMessage = "Email or phone number is required")]
        public string EmailOrPhone { get; set; }

    
    }

    public class RegisterRequest
    {
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; }

        [Required]
        [StringLength(50)]
        public string LastName { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; }

        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; }

        [Required]
        [StringLength(100, MinimumLength = 6)]
        public string Password { get; set; }

        [Required]
        [Compare("Password")]
        public string ConfirmPassword { get; set; }

        // Optional student information
        public DateTime? DateOfBirth { get; set; }
        public string? Address { get; set; }

        [Required]
        [StringLength(20)]
        public string Gender { get; set; }

        [Required]
        [StringLength(100)]
        public string Citizenship { get; set; }

        [Required]
        [StringLength(100)]
        public string CountryOfResidence { get; set; }
         [Required]
   
        public string lang { get; set; }
        [Required]

        public bool isholand { get; set; }=false;
        
        // Optional referral code from referring student
        public string? ReferralCode { get; set; }
    }

    public class ConsultantDto
    {
        public int Id { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string PhoneNumber { get; set; }
        public string ImageURL { get; set; }
        public string Description { get; set; }
        public DateTime DateCreated { get; set; }
        public DateTime DateModified { get; set; }
    }

    public class AuthResponse
    {
        public string Token { get; set; }
        public string UserId { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string ImageUrl { get; set; }
        public DateTime Expiration { get; set; }
        public Dictionary<string, string> Claims { get; set; }
        public ConsultantDto Consultant { get; set; }
        public string RefCode { get; set; }
        public int Point { get; set; }
    }
}