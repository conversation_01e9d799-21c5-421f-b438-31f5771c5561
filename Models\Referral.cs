using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace StudentManagementAPI.Models
{
    public class Referral
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public string ReferralCode { get; set; }
        
        [Required]
        public string ReferrerId { get; set; } // The student who referred
        
        [Required]
        public string ReferredStudentId { get; set; } // The student who was referred
        
        public DateTime ReferralDate { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        [ForeignKey(nameof(ReferrerId))]
        public virtual Student Referrer { get; set; }
        
        [ForeignKey(nameof(ReferredStudentId))]
        public virtual Student ReferredStudent { get; set; }
    }
}
