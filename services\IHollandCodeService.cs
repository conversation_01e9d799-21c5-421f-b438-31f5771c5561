using System.Collections.Generic;
using System.Threading.Tasks;
using StudentManagementAPI.Models;

namespace StudentManagementAPI.Services
{
    public interface IHollandCodeService
    {
        Task<HollandCodeResult> GetMajorsByHollandCodeAsync(string hollandCode, string language = "en");
    }

    public class HollandCodeResult
    {
        public string HollandCode { get; set; } = string.Empty;
        public string CodeDescription { get; set; } = string.Empty;
        public List<string> SuggestedJobs { get; set; } = new();
        public List<string> Majors { get; set; } = new();
        public UniversityMajor UniversityMajor { get; set; }
    }
}
