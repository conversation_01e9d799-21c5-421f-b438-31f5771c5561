{"info": {"name": "Test Attempts API Collection", "description": "Collection for managing student test attempts", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Start Test Attempt", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"testName\": \"Sample Test\",\n  \"studentId\": \"{{studentId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/testattempts/start", "host": ["{{baseUrl}}"], "path": ["api", "testattempts", "start"]}, "description": "Start a new test attempt for a student"}}, {"name": "Pause Test Attempt", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}/pause", "host": ["{{baseUrl}}"], "path": ["api", "testattempts", "{{testAttemptId}}", "pause"]}, "description": "Pause an ongoing test attempt"}}, {"name": "Resume Test Attempt", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}/resume", "host": ["{{baseUrl}}"], "path": ["api", "testattempts", "{{testAttemptId}}", "resume"]}, "description": "Resume a paused test attempt"}}, {"name": "Submit Test Attempt", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"answers\": [\n    {\n      \"questionId\": \"string\",\n      \"selectedAnswer\": \"string\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}/submit", "host": ["{{baseUrl}}"], "path": ["api", "testattempts", "{{testAttemptId}}", "submit"]}, "description": "Submit answers for a test attempt"}}, {"name": "Get Test Attempt Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/testattempts/{{testAttemptId}}", "host": ["{{baseUrl}}"], "path": ["api", "testattempts", "{{testAttemptId}}"]}, "description": "Get details of a specific test attempt"}}, {"name": "Get Student Test Attempts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/testattempts/student/{{studentId}}", "host": ["{{baseUrl}}"], "path": ["api", "testattempts", "student", "{{studentId}}"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}, "description": "Get all test attempts for a specific student with pagination"}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "token", "value": "your-auth-token", "type": "string"}, {"key": "studentId", "value": "student-guid", "type": "string"}, {"key": "testAttemptId", "value": "test-attempt-guid", "type": "string"}]}