{"id": "7e9d5a8b-2f1f-4e8c-9a5d-f8e6e4d2c1b0", "name": "Student Management API Environment", "description": "Environment variables for the Student Management API. Import this environment and select it before running any requests.", "values": [{"key": "baseUrl", "value": "http://localhost:5188", "description": "Base URL for the API - change this if your API is running on a different port", "type": "default", "enabled": true}, {"key": "authToken", "value": "", "description": "Authentication token - automatically populated after successful login", "type": "secret", "enabled": true}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "value": "JkGwZj3mQyT7vB8fN2xQz9LrP4dK2mV8Q6pZtH1bR5g=", "description": "API key required for all requests", "type": "secret", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-20T12:00:00.000Z", "_postman_exported_using": "Postman/10.21.9"}